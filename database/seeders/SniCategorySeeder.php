<?php

namespace Database\Seeders;

use App\Models\SniCategory;
use Illuminate\Database\Seeder;

class SniCategorySeeder extends Seeder
{
    public function run(): void
    {
        $categories = [
            ['id' => 1, 'name' => 'navigation', 'label' => 'Навигация и карты', 'enabled' => true],
            ['id' => 2, 'name' => 'messengers', 'label' => 'Мессенджеры', 'enabled' => true],
            ['id' => 3, 'name' => 'social_media', 'label' => 'Социальные сети', 'enabled' => true],
            ['id' => 4, 'name' => 'cloud_services', 'label' => 'Облачные сервисы', 'enabled' => true],
            ['id' => 5, 'name' => 'media_streaming', 'label' => 'Медиа и стриминг', 'enabled' => true],
            ['id' => 6, 'name' => 'software_services', 'label' => 'Системные и софт сервисы', 'enabled' => true],
            ['id' => 7, 'name' => 'other_services', 'label' => 'Прочие сервисы', 'enabled' => true],
            ['id' => 8, 'name' => 'russian_local', 'label' => 'Российские сервисы', 'enabled' => false],
        ];

        foreach ($categories as $category) {
            SniCategory::updateOrCreate(['id' => $category['id']], $category);
        }
    }
}
