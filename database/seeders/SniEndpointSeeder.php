<?php

namespace Database\Seeders;

use App\Models\SniEndpoint;
use Illuminate\Database\Seeder;

class SniEndpointSeeder extends Seeder
{
    public function run(): void
    {
        $endpoints = [
            // Google Maps
            ['provider_id'=>1,'domain'=>'maps.google.com','name'=>'Google Maps Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Google CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // Apple Maps
            ['provider_id'=>2,'domain'=>'maps.apple.com','name'=>'Apple Maps CDN','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Apple CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // Bing Maps
            ['provider_id'=>3,'domain'=>'www.bing.com','name'=>'Bing Maps Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Microsoft CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>3,'domain'=>'dev.virtualearth.net','name'=>'Bing Dev API','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Microsoft CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // OpenStreetMap
            ['provider_id'=>4,'domain'=>'tile.openstreetmap.org','name'=>'OSM Tile Server','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'DE','cdn_provider'=>'OpenStreetMap','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>4,'domain'=>'a.tile.openstreetmap.de','name'=>'OSM DE Mirror','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'DE','cdn_provider'=>'OpenStreetMap','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>4,'domain'=>'b.tile.openstreetmap.fr','name'=>'OSM FR Mirror','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'FR','cdn_provider'=>'OpenStreetMap','priority'=>1,'verified'=>true,'used_times'=>0],

            // Mapbox
            ['provider_id'=>5,'domain'=>'api.mapbox.com','name'=>'Mapbox API','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Mapbox CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>5,'domain'=>'events.mapbox.com','name'=>'Mapbox Telemetry','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Mapbox CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // WhatsApp
            ['provider_id'=>6,'domain'=>'web.whatsapp.com','name'=>'WhatsApp Web','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Facebook CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>6,'domain'=>'mmg.whatsapp.net','name'=>'WhatsApp Media','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Facebook CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // Telegram
            ['provider_id'=>7,'domain'=>'telegram.org','name'=>'Telegram Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'NL','cdn_provider'=>'Telegram CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>7,'domain'=>'cdn.telegram.org','name'=>'Telegram CDN','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'NL','cdn_provider'=>'Telegram CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>7,'domain'=>'web.telegram.org','name'=>'Telegram Web','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'NL','cdn_provider'=>'Telegram CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // snapchat
            ['provider_id'=>8,'domain'=>'www.snapchat.com','name'=>'Snapchat Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Snapchat CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>8,'domain'=>'help.snapchat.com','name'=>'Snapchat Help','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Snapchat CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>8,'domain'=>'static.snapchat.com','name'=>'Snapchat CDN','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Snapchat CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // imo
            ['provider_id'=>11,'domain'=>'imo.im','name'=>'IMO Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'IMO CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>11,'domain'=>'api.imo.im','name'=>'IMO API','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'IMO CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>11,'domain'=>'cdn.imo.im','name'=>'IMO CDN','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'IMO CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // tamtam
            ['provider_id'=>10,'domain'=>'tamtam.chat','name'=>'Tamtam Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Tamtam CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>10,'domain'=>'api.tamtam.chat','name'=>'Tamtam API','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Tamtam CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>10,'domain'=>'web.tamtam.chat','name'=>'Tamtam Web','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Tamtam CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>10,'domain'=>'cdn.tamtam.chat','name'=>'Tamtam CDN','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Tamtam CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>10,'domain'=>'media.tamtam.chat','name'=>'Tamtam Media','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Tamtam CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // imessage
            ['provider_id'=>11,'domain'=>'s.mzstatic.com','name'=>'iMessage (mzstatic.com)','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Apple CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // TikTok
            ['provider_id'=>12,'domain'=>'www.tiktok.com','name'=>'TikTok Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'TikTok CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>12,'domain'=>'api16-normal-c-useast1a.tiktokv.com','name'=>'TikTok API US','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'TikTok CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>12,'domain'=>'v16m.tiktokcdn.com','name'=>'TikTok CDN','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'TikTok CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>12,'domain'=>'mon.tiktokv.com','name'=>'TikTok Monitor','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'TikTok CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>12,'domain'=>'sf16-website-login.neutral.ttwstatic.com','name'=>'TikTok API US','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'TikTok CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // Pinterest
            ['provider_id'=>13,'domain'=>'api.pinterest.com','name'=>'Pinterest API','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Pinterest CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>13,'domain'=>'www.pinterest.com','name'=>'Pinterest Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Pinterest CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>13,'domain'=>'s.pinimg.com','name'=>'Pinterest CDN','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Pinterest CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>13,'domain'=>'i.pinimg.com','name'=>'Pinterest Image CDN','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Pinterest CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // Reddit
            ['provider_id'=>14,'domain'=>'www.reddit.com','name'=>'Reddit Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>57,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Fastly','priority'=>1,'verified'=>true,'used_times'=>0],

            // mastodon
            ['provider_id'=>15,'domain'=>'mastodon.social','name'=>'Mastodon Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Mastodon CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // flickr
            ['provider_id'=>16,'domain'=>'flickr.com','name'=>'Flickr Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Flickr CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>16,'domain'=>'combo.staticflickr.com','name'=>'Flickr CDN','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Flickr CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // cloudflare
            ['provider_id'=>17,'domain'=>'speed.cloudflare.com','name'=>'Cloudflare Speed','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Cloudflare','priority'=>1,'verified'=>true,'used_times'=>0],

            // open.spotify.com
            ['provider_id'=>18,'domain'=>'open.spotify.com','name'=>'Spotify Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Spotify CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>18,'domain'=>'gew4-spclient.spotify.com','name'=>'Spotify Client','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Spotify CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // Zoom
            ['provider_id'=>19,'domain'=>'zoom.com','name'=>'Zoom Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>64,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Zoom CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // microsoft
            ['provider_id'=>20,'domain'=>'www.office.com','name'=>'Microsoft Office','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Microsoft CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>20,'domain'=>'www.microsoft365.com','name'=>'Microsoft 365','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Microsoft CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // apple
            ['provider_id'=>21,'domain'=>'apple.com','name'=>'Apple Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Apple CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>21,'domain'=>'developer.apple.com','name'=>'Apple Developer','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Apple CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // google
            ['provider_id'=>22,'domain'=>'google.com','name'=>'Google Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Google CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>22,'domain'=>'www.google.com','name'=>'Google WWW','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Google CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // mozilla
            ['provider_id'=>23,'domain'=>'download-installer.cdn.mozilla.net','name'=>'Mozilla CDN','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Mozilla CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // ubuntu
            ['provider_id'=>24,'domain'=>'mirror.kumi.systems','name'=>'Austria Mirror','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Ubuntu CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>24,'domain'=>'mirror.nl.mirhosting.net','name'=>'Mirhosting Mirror','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Ubuntu CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>24,'domain'=>'mirror.lyrahosting.com','name'=>'Lyra Hosting Mirror','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Ubuntu CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // libreoffice
            ['provider_id'=>25,'domain'=>'www.libreoffice.org','name'=>'LibreOffice Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'LibreOffice CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // onlyoffice
            ['provider_id'=>26,'domain'=>'static-www.onlyoffice.com','name'=>'OnlyOffice CDN','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'OnlyOffice CDN','priority'=>1,'verified'=>true,'used_times'=>0],

            // yahoo
            ['provider_id'=>27,'domain'=>'www.yahoo.com','name'=>'Yahoo Main (www)','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Yahoo CDN','priority'=>1,'verified'=>true,'used_times'=>0],
            ['provider_id'=>27,'domain'=>'yahoo.com','name'=>'Yahoo Main','enabled'=>true,'accessible'=>true,'tls_valid'=>true,'ping'=>null,'last_ping_at'=>null,'last_checked_at'=>null,'country'=>'US','cdn_provider'=>'Yahoo CDN','priority'=>1,'verified'=>true,'used_times'=>0],
        ];


        foreach ($endpoints as $endpoint) {
            SniEndpoint::updateOrCreate(
                ['domain' => $endpoint['domain'], 'provider_id' => $endpoint['provider_id']],
                $endpoint
            );
        }
    }
}
