<?php

namespace Database\Seeders;

use App\Models\SniEndpoint;
use App\Models\SniTag;
use Illuminate\Database\Seeder;

class SniEndpointTagSeeder extends Seeder
{
    public function run(): void
    {
        $relations = [
            ['sni_endpoint_id' => 1, 'tag_id' => 1], //
            ['sni_endpoint_id' => 1, 'tag_id' => 2], //
            ['sni_endpoint_id' => 1, 'tag_id' => 3], //

            ['sni_endpoint_id' => 2, 'tag_id' => 1],
            ['sni_endpoint_id' => 2, 'tag_id' => 2],
            ['sni_endpoint_id' => 2, 'tag_id' => 3],

            ['sni_endpoint_id' => 3, 'tag_id' => 1],
            ['sni_endpoint_id' => 3, 'tag_id' => 4],

            ['sni_endpoint_id' => 4, 'tag_id' => 1],
            ['sni_endpoint_id' => 4, 'tag_id' => 6],

            ['sni_endpoint_id' => 1, 'tag_id' => 13], // maps.google.com
            ['sni_endpoint_id' => 2, 'tag_id' => 13], // maps.apple.com
            ['sni_endpoint_id' => 20, 'tag_id' => 13], // web.whatsapp.com
            ['sni_endpoint_id' => 17, 'tag_id' => 13], // static.snapchat.com
            ['sni_endpoint_id' => 20, 'tag_id' => 13], // cdn.imo.im
            ['sni_endpoint_id' => 48, 'tag_id' => 13], // google.com
            ['sni_endpoint_id' => 49, 'tag_id' => 13], // www.google.com
            ['sni_endpoint_id' => 56, 'tag_id' => 13], // www.yahoo.com
            ['sni_endpoint_id' => 57, 'tag_id' => 13], // yahoo.com
        ];

        foreach ($relations as $relation) {
            $endpoint = SniEndpoint::find($relation['sni_endpoint_id']);
            $tag = SniTag::find($relation['tag_id']);

            if ($endpoint && $tag) {
                $endpoint->sniTags()->syncWithoutDetaching([$tag->id]);
            }
        }
    }
}
