<?php

namespace Database\Seeders;

use App\Models\SniTag;
use Illuminate\Database\Seeder;

class SniTagSeeder extends Seeder
{
    public function run(): void
    {
        $tags = [
            ['id' => 1, 'name' => 'cdn'],
            ['id' => 2, 'name' => 'google'],
            ['id' => 3, 'name' => 'maps'],
            ['id' => 6, 'name' => 'microsoft'],
            ['id' => 7, 'name' => 'apple'],
            ['id' => 8, 'name' => 'office'],
            ['id' => 9, 'name' => 'm365'],
            ['id' => 10, 'name' => 'bing'],
            ['id' => 11, 'name' => 'secure'],
            ['id' => 12, 'name' => 'fast'],
            ['id' => 13, 'name' => 'optimal'],
        ];

        foreach ($tags as $tag) {
            SniTag::updateOrCreate(['id' => $tag['id']], $tag);
        }
    }
}
