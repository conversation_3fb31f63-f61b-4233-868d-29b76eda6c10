<?php

namespace Database\Seeders;

use App\Models\SniProvider;
use Illuminate\Database\Seeder;

class SniProviderSeeder extends Seeder
{
    public function run(): void
    {
        $providers = [
            // Navigation
            ['id' => 1,  'category_id' => 1, 'name' => 'google',          'label' => 'Google',           'enabled' => true],
            ['id' => 2,  'category_id' => 1, 'name' => 'apple',           'label' => 'Apple',            'enabled' => true],
            ['id' => 3,  'category_id' => 1, 'name' => 'bing',            'label' => 'Bing (Microsoft)', 'enabled' => true],
            ['id' => 4,  'category_id' => 1, 'name' => 'openstreetmap',   'label' => 'OpenStreetMap',    'enabled' => true],
            ['id' => 5,  'category_id' => 1, 'name' => 'mapbox',          'label' => 'Mapbox',           'enabled' => true],

            // Messengers
            ['id' => 6,  'category_id' => 2, 'name' => 'whatsapp',        'label' => 'WhatsApp',         'enabled' => true],
            ['id' => 7,  'category_id' => 2, 'name' => 'telegram',        'label' => 'Telegram',         'enabled' => true],
            ['id' => 8, 'category_id' => 2, 'name' => 'snapchat',        'label' => 'Snapchat',         'enabled' => true],
            ['id' => 9, 'category_id' => 2, 'name' => 'imo',             'label' => 'IMO',              'enabled' => true],
            ['id' => 10, 'category_id' => 2, 'name' => 'tamtam',          'label' => 'Tamtam',           'enabled' => true],
            ['id' => 11, 'category_id' => 2, 'name' => 'imessage',        'label' => 'iMessage',         'enabled' => true],

            // Social Media
            ['id' => 12, 'category_id' => 3, 'name' => 'tiktok',          'label' => 'TikTok',           'enabled' => true],
            ['id' => 13, 'category_id' => 3, 'name' => 'pinterest',       'label' => 'Pinterest',        'enabled' => true],
            ['id' => 14, 'category_id' => 3, 'name' => 'reddit',          'label' => 'Reddit',           'enabled' => true],
            ['id' => 15, 'category_id' => 3, 'name' => 'mastodon',        'label' => 'Mastodon',         'enabled' => true],
            ['id' => 16, 'category_id' => 3, 'name' => 'flickr',          'label' => 'Flickr',           'enabled' => true],

            // Cloud Services
            ['id' => 17, 'category_id' => 4, 'name' => 'cloudflare',      'label' => 'Cloudflare',       'enabled' => true],

            // Media Streaming
            ['id' => 18, 'category_id' => 5, 'name' => 'spotify',         'label' => 'Spotify',          'enabled' => true],
            ['id' => 19, 'category_id' => 5, 'name' => 'zoom',            'label' => 'Zoom',             'enabled' => true],

            // Software Services
            ['id' => 20, 'category_id' => 6, 'name' => 'microsoft',       'label' => 'Microsoft',        'enabled' => true],
            ['id' => 21, 'category_id' => 6, 'name' => 'apple',           'label' => 'Apple',            'enabled' => true],
            ['id' => 22, 'category_id' => 6, 'name' => 'google',          'label' => 'Google',           'enabled' => true],
            ['id' => 23, 'category_id' => 6, 'name' => 'mozilla',         'label' => 'Mozilla',          'enabled' => true],
            ['id' => 24, 'category_id' => 6, 'name' => 'ubuntu',          'label' => 'Ubuntu',           'enabled' => true],
            ['id' => 25, 'category_id' => 6, 'name' => 'libreoffice',     'label' => 'LibreOffice',      'enabled' => true],
            ['id' => 26, 'category_id' => 6, 'name' => 'onlyoffice',      'label' => 'OnlyOffice',       'enabled' => true],

            // Others
            ['id' => 27, 'category_id' => 7, 'name' => 'yahoo',      'label' => 'Yahoo',       'enabled' => true],

        ];

        foreach ($providers as $provider) {
            SniProvider::updateOrCreate(['id' => $provider['id']], $provider);
        }
    }
}
