<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add balance and combined payment methods
        DB::table('payment_methods')->insert([
            // balance, combined
            [
                'code' => 'balance',
                'short_code' => 'BAL',
                'name' => 'Balance (Баланс)',
                'description' => 'Оплатить с баланса',
                'notes_for_admins' => 'Оплата с баланса пользователя',
                'type' => 'online',
                'is_active' => true,
                'is_public' => true,
            ],
            [
                'code' => 'combined',
                'short_code' => 'COM',
                'name' => 'Combined (Комбинированная)',
                'description' => 'Оплатить с баланса и картой/QR-кодом/СБП',
                'notes_for_admins' => 'Комбинированная оплата (с баланса и внешним способом)',
                'type' => 'online',
                'is_active' => true,
                'is_public' => true,
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove balance and combined payment methods
        DB::table('payment_methods')
            ->whereIn('code', ['balance', 'combined'])
            ->delete();
    }
};
