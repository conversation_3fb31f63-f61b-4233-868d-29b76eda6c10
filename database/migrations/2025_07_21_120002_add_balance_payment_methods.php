<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add balance and combined payment methods
        DB::table('payment_methods')->insert([
            // balance only - combined payments are handled directly in PaymentService
            [
                'code' => 'balance',
                'short_code' => 'BAL',
                'name' => 'Balance (Баланс)',
                'description' => 'Оплатить с баланса',
                'notes_for_admins' => 'Оплата с баланса пользователя',
                'type' => 'online',
                'is_active' => true,
                'is_public' => true,
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove balance payment method
        DB::table('payment_methods')
            ->where('code', 'balance')
            ->delete();
    }
};
