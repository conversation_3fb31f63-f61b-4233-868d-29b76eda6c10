<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('sni_endpoints', function (Blueprint $table) {
            $table->id();
            $table->foreignId('provider_id')->constrained('sni_providers')->onDelete('cascade');
            $table->string('domain', 255);
            $table->string('name', 255);
            $table->boolean('enabled')->default(true);
            $table->boolean('accessible')->default(false);
            $table->boolean('tls_valid')->default(false);
            $table->integer('ping')->nullable();
            $table->timestamp('last_ping_at')->nullable();
            $table->timestamp('last_checked_at')->nullable();
            $table->char('country', 2)->nullable();
            $table->string('cdn_provider', 100)->nullable();
            $table->integer('priority')->default(100);
            $table->boolean('verified')->default(false);
            $table->integer('used_times')->default(0);
            $table->string('source_ip', 45)->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['provider_id', 'enabled']);
            $table->index(['enabled', 'priority']);
            $table->index('country');
            $table->index('accessible');
            $table->index('verified');
            $table->unique(['provider_id', 'domain']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('sni_endpoints');
    }
};