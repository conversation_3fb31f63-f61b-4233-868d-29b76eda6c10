<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('sni_endpoint_tag', function (Blueprint $table) {
            $table->foreignId('sni_endpoint_id')->constrained('sni_endpoints')->onDelete('cascade');
            $table->foreignId('sni_tag_id')->constrained('sni_tags')->onDelete('cascade');

            $table->primary(['sni_endpoint_id', 'sni_tag_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('sni_endpoint_tag');
    }
};
