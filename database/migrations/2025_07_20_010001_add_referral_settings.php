<?php

use App\Models\Setting;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    private array $settings = [
            ['key' => 'leadteh_ref_level_1_percent', 'value' => 30,   'description' => 'Процент вознаграждения для реферера 1-го уровня'],
            ['key' => 'leadteh_ref_level_2_percent', 'value' => 10,   'description' => 'Процент вознаграждения для реферера 2-го уровня'],
            ['key' => 'leadteh_ref_level_3_percent', 'value' => 5,    'description' => 'Процент вознаграждения для реферера 3-го уровня'],
            ['key' => 'leadteh_ref_enabled',         'value' => true, 'description' => 'Включена ли реферальная система'],
            ['key' => 'leadteh_ref_min_amount',      'value' => 1000, 'description' => 'Минимальная сумма для начисления реферальных в копейках (1000 коп = 10 руб)'],
        ];

    public function up(): void
    {
        foreach ($this->settings as $setting) {
            Setting::set($setting['key'], $setting['value'], $setting['description']);
        }
    }

    public function down(): void
    {
        $keys = array_column($this->settings, 'key');
        Setting::forgetMultiple($keys);
    }
};
