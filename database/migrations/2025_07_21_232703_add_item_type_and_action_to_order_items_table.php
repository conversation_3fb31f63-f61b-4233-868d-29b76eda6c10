<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->enum('item_type', ['subscription_plan', 'addon', 'custom', 'balance_topup'])->change();
            $table->enum('action', ['new', 'renew', 'upgrade', 'extend', 'downgrade', 'topup'])->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->enum('item_type', ['subscription_plan', 'addon', 'custom'])->change();
            $table->enum('action', ['new', 'renew', 'upgrade', 'extend', 'downgrade'])->change();
        });
    }
};
