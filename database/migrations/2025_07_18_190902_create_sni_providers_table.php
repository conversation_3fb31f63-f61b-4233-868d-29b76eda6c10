<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('sni_providers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained('sni_categories')->onDelete('cascade');
            $table->string('name', 100);
            $table->string('label', 255);
            $table->boolean('enabled')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['category_id', 'enabled']);
            $table->unique(['category_id', 'name']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('sni_providers');
    }
};