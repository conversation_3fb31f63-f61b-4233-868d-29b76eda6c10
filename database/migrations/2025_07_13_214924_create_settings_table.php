<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * Настройки приложения
         */
        Schema::create('settings', function (Blueprint $table) {
            $table->id()->primary();
            $table->string('key')->unique();
            $table->longText('value')->nullable();
            $table->enum('type', ['string', 'int', 'float', 'bool', 'array', 'json'])->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();
        });

        // Insert default common_routing_rules setting
        DB::table('settings')->insert([
            'key' => 'common_routing_rules',
            'value' => '', // Значение будет вставлено вручную
            'type' => 'json',
            'notes' => 'Common routing rules for VPN clients (base64 encoded).',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        DB::table('settings')->insert([
            'key' => 'renewal_threshold_days',
            'value' => '3', // Значение будет вставлено вручную
            'type' => 'int',
            'notes' => 'Number of days before the subscription expires. For example, before which the renewal button will be displayed',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
