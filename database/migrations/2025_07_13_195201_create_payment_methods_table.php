<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * Способы оплаты
         */
        Schema::create('payment_methods', function (Blueprint $table) {
            $table->id()->primary();

            $table->string('code', 32)->unique()->comment('Unique code for internal use, e.g. "stripe", "yookassa", "cash", "manual", "free"');
            $table->string('short_code', 10)->unique()->comment('Short code for public display, e.g. "YC", "YKSS" and etc is for YooKassa');

            $table->string('name')->comment('Name for public display, e.g. "YooKassa"');
            $table->string('description')->nullable()->comment('Description for public display');
            $table->string('notes_for_admins')->nullable()->comment('Notes for admins');
            $table->enum('type', ['online', 'offline', 'manual']);
            $table->boolean('is_active')->default(false);
            $table->boolean('is_public')->default(false)->comment('Whether the method is available for public use');

            $table->timestamps();
            // soft deletes
            $table->softDeletes();
        });

        // Insert default payment methods that are available in Services/Payment/Gateways
        DB::table('payment_methods')->insert([
            [
                'code' => 'manual',
                'short_code' => 'MNL',
                'name' => 'Manual (Ручная оплата)',
                'description' => null,
                'notes_for_admins' => 'Ручная оплата. Подходит, если платеж получен как перевод на карту, получен на иностранный счет, иной валютой, криптой и т.д.',
                'type' => 'manual',
                'is_active' => true,
                'is_public' => false,
            ],
            [
                'code' => 'cash',
                'short_code' => 'CSH',
                'name' => 'Cash (Наличные)',
                'description' => null,
                'notes_for_admins' => 'Оплата наличными. Подходит, если нужно зафиксировать именно оплату наличными.',
                'type' => 'offline',
                'is_active' => true,
                'is_public' => false,
            ],
            [
                'code' => 'free',
                'short_code' => 'FR',
                'name' => 'Free (Бесплатно)',
                'description' => null,
                'notes_for_admins' => 'Бесплатный способ оплаты. Подходит, если нужно выдать подписку бесплатно.',
                'type' => 'manual',
                'is_active' => true,
                'is_public' => false,
            ],
            [
                'code' => 'tbank',
                'short_code' => 'TBNK',
                'name' => 'T-Bank (ex. Tinkoff)',
                'description' => 'Оплата картами МИР/QR-код/СБП, для РФ 🇷🇺',
                'notes_for_admins' => 'Способ оплаты через T-Bank (ex. Tinkoff)',
                'type' => 'online',
                'is_active' => true,
                'is_public' => true,
            ],
        ]);

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};
