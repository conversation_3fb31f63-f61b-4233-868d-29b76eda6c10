<?php

use App\DTOs\Balance\BalanceTransactionDTO;
use App\Enums\BalanceTransactionSource;
use App\Enums\BalanceTransactionType;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('balance_transactions', function (Blueprint $table) {
            $table->uuid('id')->primary();

            $table->uuid('user_id');
            $table->enum('type', ['credit', 'debit'])->comment('Transaction type: credit (add funds) or debit (deduct funds)');
            $table->enum('source', [
                'topup',
                'referral',
                'refund',
                'order_payment',
                'bonus',
                'manual_adjustment'
            ])->comment('Source of the transaction');
            $table->integer('amount')->comment('Transaction amount in minor units (kopecks, cents, etc.)');
            $table->integer('balance_before')->comment('User balance before transaction in minor units');
            $table->integer('balance_after')->comment('User balance after transaction in minor units');
            $table->string('currency', 4)->default('RUB');
            $table->text('description')->nullable()->comment('Optional description or notes for the transaction');

            // Optional references to related entities
            $table->uuid('order_id')->nullable()->comment('Related order ID if applicable');
            $table->uuid('payment_id')->nullable()->comment('Related payment ID if applicable');

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('user_id')
                ->references('id')
                ->on('users')
                ->onDelete('cascade');

            $table->foreign('order_id')
                ->references('id')
                ->on('orders')
                ->onDelete('set null');

            $table->foreign('payment_id')
                ->references('id')
                ->on('payments')
                ->onDelete('set null');

            // Indexes for performance
            $table->index(['user_id', 'created_at']);
            $table->index(['user_id', 'type']);
            $table->index(['user_id', 'source']);
            $table->index(['order_id']);
            $table->index(['payment_id']);
            $table->index(['type', 'source']);
            $table->index(['created_at']);
        });

        // seed test data for first user using Model and DTO
        $initialTopups = [
            [
                'amount' => 15000,
                'description' => 'Initial balance for testing',
            ],
            [
                'amount' => 5000,
                'description' => 'Second top-up for testing',
            ],
        ];

        $user = User::first();
        if ($user) {
            foreach ($initialTopups as $topup) {
                $dto = new BalanceTransactionDTO(
                    $user,
                    BalanceTransactionType::CREDIT,
                    BalanceTransactionSource::TOPUP,
                    $topup['amount'],
                    $topup['description'],
                );

                // topup by service
                app(\App\Services\BalanceService::class)->addFunds(
                    $dto->user,
                    $dto->amount,
                    $dto->source,
                    $dto->description
                );
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('balance_transactions');
    }
};
