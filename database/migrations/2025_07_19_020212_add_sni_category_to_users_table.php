<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('sni_category')->nullable()->comment('The SNI category for the user');

            // foreign to sni_categories
            $table->foreign('sni_category')
                    ->references('name')
                    ->on('sni_categories')
                    ->onDelete('set null');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign('users_sni_category_foreign');
            $table->dropColumn('sni_category');
        });
    }
};
