<?php

namespace Cknow\Money;

/**
 * Money Factory.
 *
 * @method static Money AED(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money ALL(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money AMD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money ANG(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money AOA(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money ARS(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money AUD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money AWG(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money AZN(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BAM(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BBD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BDT(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BGN(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BHD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BIF(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BMD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BND(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BOB(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BOV(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BRL(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BSD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BTN(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BWP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BYN(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money BZD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money CAD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money CDF(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money CHE(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money CHF(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money CHW(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money CLF(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money CLP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money CNY(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money COP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money COU(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money CRC(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money CUC(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money CUP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money CVE(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money CZK(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money DJF(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money DKK(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money DOP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money DZD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money EGP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money ERN(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money ETB(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money EUR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money FJD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money FKP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money GBP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money GEL(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money GHS(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money GIP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money GMD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money GNF(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money GTQ(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money GYD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money HKD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money HNL(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money HRK(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money HTG(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money HUF(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money IDR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money ILS(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money INR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money IQD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money IRR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money ISK(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money JMD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money JOD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money JPY(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money KES(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money KGS(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money KHR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money KMF(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money KPW(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money KRW(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money KWD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money KYD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money KZT(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money LAK(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money LBP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money LKR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money LRD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money LSL(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money LYD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money MAD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money MDL(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money MGA(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money MKD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money MMK(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money MNT(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money MOP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money MRU(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money MUR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money MVR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money MWK(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money MXN(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money MXV(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money MYR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money MZN(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money NAD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money NGN(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money NIO(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money NOK(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money NPR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money NZD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money OMR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money PAB(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money PEN(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money PGK(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money PHP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money PKR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money PLN(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money PYG(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money QAR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money RON(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money RSD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money RUB(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money RWF(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money SAR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money SBD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money SCR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money SDG(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money SEK(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money SGD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money SHP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money SLL(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money SOS(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money SRD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money SSP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money STN(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money SVC(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money SYP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money SZL(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money THB(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money TJS(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money TMT(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money TND(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money TOP(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money TRY(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money TTD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money TWD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money TZS(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money UAH(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money UGX(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money USD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money USN(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money UYI(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money UYU(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money UYW(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money UZS(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money VES(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money VND(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money VUV(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money WST(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XAF(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XAG(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XAU(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XBA(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XBB(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XBC(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XBD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XBT(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XCD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XDR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XOF(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XPD(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XPF(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XPT(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XSU(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XTS(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XUA(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money XXX(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money YER(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money ZAR(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money ZMW(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 * @method static Money ZWL(mixed $amount, bool $forceDecimals = false, string|null $locale = null, \Money\Currencies|null $currencies = null)
 */
trait MoneyFactory
{
    /**
     * __callStatic.
     *
     * @param  string  $method
     * @return \Cknow\Money\Money
     */
    public static function __callStatic($method, array $arguments)
    {
        return new Money(
            $arguments[0],
            Money::parseCurrency($method),
            $arguments[1] ?? false,
            $arguments[2] ?? null,
            $arguments[3] ?? null
        );
    }

    /**
     * Create a new instance from the base money instance.
     *
     * @return \Cknow\Money\Money
     */
    public static function fromMoney(\Money\Money $instance)
    {
        return new Money($instance->getAmount(), $instance->getCurrency());
    }
}
