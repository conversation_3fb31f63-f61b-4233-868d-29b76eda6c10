<?php

namespace Tests\Feature;

use App\Services\PaymentService;
use App\Services\BalanceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CircularDependencyTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function payment_service_can_be_instantiated_without_circular_dependency()
    {
        // This should not cause a circular dependency error
        $paymentService = app(PaymentService::class);
        
        $this->assertInstanceOf(PaymentService::class, $paymentService);
    }

    /** @test */
    public function balance_service_can_be_instantiated_without_circular_dependency()
    {
        // This should not cause a circular dependency error
        $balanceService = app(BalanceService::class);
        
        $this->assertInstanceOf(BalanceService::class, $balanceService);
    }

    /** @test */
    public function payment_gateways_can_be_created_without_circular_dependency()
    {
        $paymentService = app(PaymentService::class);
        
        // Test that all gateways can be created without circular dependency
        $gateways = ['tbank', 'free', 'manual', 'cash', 'balance'];
        
        foreach ($gateways as $gatewayCode) {
            $gateway = $paymentService->getGateway($gatewayCode);
            $this->assertNotNull($gateway, "Gateway {$gatewayCode} should be created successfully");
        }
    }

    /** @test */
    public function combined_payment_gateway_is_not_registered_to_avoid_circular_dependency()
    {
        $paymentService = app(PaymentService::class);
        
        // Combined gateway should not be available through getGateway to avoid circular dependency
        $gateway = $paymentService->getGateway('combined');
        $this->assertNull($gateway, "Combined gateway should not be registered to avoid circular dependency");
    }
}
