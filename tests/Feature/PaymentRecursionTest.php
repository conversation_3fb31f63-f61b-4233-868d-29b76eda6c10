<?php

namespace Tests\Feature;

use App\Models\Order;
use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Services\PaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PaymentRecursionTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function payment_creation_does_not_cause_recursion()
    {
        // Create test data
        $user = User::factory()->create();
        $paymentMethod = PaymentMethod::factory()->create(['code' => 'free']);
        
        // Create an order
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'total_amount' => 0, // Free order
            'status' => 'pending',
        ]);

        // Create payment service
        $paymentService = app(PaymentService::class);

        // This should not cause recursion
        $result = $paymentService->createPayment($order, 'free');

        $this->assertTrue($result->isSuccess());
        $this->assertEquals('paid', $result->status);

        // Verify order is marked as paid
        $order->refresh();
        $this->assertEquals('paid', $order->status);
        $this->assertNotNull($order->paid_at);

        // Verify payment is created and marked as paid
        $payment = $order->payments()->first();
        $this->assertNotNull($payment);
        $this->assertEquals('paid', $payment->status);
        $this->assertNotNull($payment->paid_at);
    }

    /** @test */
    public function payment_mark_as_paid_does_not_cause_recursion()
    {
        // Create test data
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'status' => 'pending',
        ]);
        
        $payment = Payment::factory()->create([
            'order_id' => $order->id,
            'status' => 'pending',
        ]);

        // This should not cause recursion
        $payment->markAsPaid();

        // Verify payment is marked as paid
        $payment->refresh();
        $this->assertEquals('paid', $payment->status);
        $this->assertNotNull($payment->paid_at);

        // Order status should be updated by the event listener, not directly by Payment::markAsPaid()
        // This verifies that we removed the recursive call
    }

    /** @test */
    public function order_mark_as_paid_does_not_cause_recursion()
    {
        // Create test data
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'status' => 'pending',
        ]);

        // This should not cause recursion
        $order->markAsPaid();

        // Verify order is marked as paid
        $order->refresh();
        $this->assertEquals('paid', $order->status);
        $this->assertNotNull($order->paid_at);
    }
}
