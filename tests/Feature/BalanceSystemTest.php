<?php

namespace Tests\Feature;

use App\Enums\BalanceTransactionSource;
use App\Models\User;
use App\Services\BalanceService;
use App\Services\MoneyService;
use App\Services\OrderService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BalanceSystemTest extends TestCase
{
    use RefreshDatabase;

    private BalanceService $balanceService;
    private OrderService $orderService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->balanceService = app(BalanceService::class);
        $this->orderService = app(OrderService::class);
    }

    /** @test */
    public function user_starts_with_zero_balance()
    {
        $user = User::factory()->create();

        $this->assertEquals(0, $user->getTotalBalanceAmount());
        $this->assertEquals(0.0, $user->getBalance());
        $this->assertFalse($user->hasPositiveBalance());
        $this->assertFalse($user->hasNegativeBalance());
    }

    /** @test */
    public function can_add_funds_to_user_balance()
    {
        $user = User::factory()->create();
        $amount = 100.50; // 100.50 RUB

        $transaction = $this->balanceService->addFunds(
            user: $user,
            amount: $amount,
            source: BalanceTransactionSource::TOPUP,
            description: 'Test top-up'
        );

        // Refresh user to get updated balance
        $user->refresh();

        $this->assertEquals(10000, $user->getTotalBalanceAmount()); // 100.00 in kopecks
        $this->assertEquals(10000, $user->getBalance());
        $this->assertTrue($user->hasPositiveBalance());
        $this->assertTrue($user->canAfford(5000));
        $this->assertTrue($user->canAfford(10000));
        $this->assertFalse($user->canAfford(10051));

        // Check transaction
        $this->assertEquals($user->id, $transaction->user_id);
        $this->assertEquals(10050, $transaction->amount);
        $this->assertEquals(0, $transaction->balance_before);
        $this->assertEquals(10050, $transaction->balance_after);
        $this->assertTrue($transaction->isCredit());
    }

    /** @test */
    public function can_deduct_funds_from_user_balance()
    {
        $user = User::factory()->create();

        // First add some funds
        $this->balanceService->addFunds(
            user: $user,
            amount: 100.0,
            source: BalanceTransactionSource::TOPUP
        );

        // Then deduct some
        $transaction = $this->balanceService->deductFunds(
            user: $user,
            amount: 30.0,
            source: BalanceTransactionSource::ORDER_PAYMENT,
            description: 'Test payment'
        );

        $user->refresh();

        $this->assertEquals(7000, $user->getTotalBalanceAmount()); // 70.00 in kopecks
        $this->assertEquals(700, $user->getBalance());
        $this->assertTrue($user->hasPositiveBalance());

        // Check transaction
        $this->assertEquals(3000, $transaction->amount);
        $this->assertEquals(10000, $transaction->balance_before);
        $this->assertEquals(7000, $transaction->balance_after);
        $this->assertTrue($transaction->isDebit());
    }

    /** @test */
    public function can_create_balance_topup_order()
    {
        $user = User::factory()->create();
        $amount = 500;

        $order = $this->orderService->createBalanceTopUpOrder(
            user: $user,
            amount: $amount,
            currency: 'RUB',
            description: 'Test top-up order'
        );

        $this->assertEquals($user->id, $order->user_id);
        $this->assertEquals(5000, $order->total_amount); // 50.00 in kopecks
        $this->assertEquals('RUB', $order->currency);
        $this->assertEquals('pending', $order->status);

        // Check order item
        $orderItem = $order->items()->first();
        $this->assertEquals('balance_topup', $orderItem->item_type);
        $this->assertEquals('topup', $orderItem->action);
        $this->assertEquals(5000, $orderItem->price);
    }

    /** @test */
    public function can_process_balance_only_payment()
    {
        $user = User::factory()->create();

        // Add funds to balance
        $this->balanceService->addFunds(
            user: $user,
            amount: 10000,
            source: BalanceTransactionSource::TOPUP
        );

        // Create an order
        $order = $this->orderService->createBalanceTopUpOrder(
            user: $user,
            amount: 5000
        );

        // Process balance-only payment
        $success = $this->orderService->processBalanceOnlyPayment($order);

        $this->assertTrue($success);

        $order->refresh();
        $user->refresh();

        // Check order status
        $this->assertEquals('paid', $order->status);
        $this->assertNotNull($order->paid_at);
        $this->assertEquals(5000, $order->balance_used_amount);
        $this->assertNotNull($order->balance_transaction_id);

        // Check user balance
        $this->assertEquals(5000, $user->getTotalBalanceAmount()); // 50.00 remaining
        $this->assertEquals(500, $user->getBalance());
    }

    /** @test */
    public function cannot_pay_with_insufficient_balance()
    {
        $user = User::factory()->create();

        // Add small amount to balance
        $this->balanceService->addFunds(
            user: $user,
            amount: 1000,
            source: BalanceTransactionSource::TOPUP
        );

        // Create an order for larger amount
        $order = $this->orderService->createBalanceTopUpOrder(
            user: $user,
            amount: 5000
        );

        // Try to process balance-only payment
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Insufficient balance');

        $this->orderService->processBalanceOnlyPayment($order);
    }

    /** @test */
    public function balance_transactions_are_tracked_correctly()
    {
        $user = User::factory()->create();

        // Add funds
        $this->balanceService->addFunds(
            user: $user,
            amount: 10000,
            source: BalanceTransactionSource::TOPUP,
            description: 'Initial top-up'
        );

        // Deduct funds
        $this->balanceService->deductFunds(
            user: $user,
            amount: 3000,
            source: BalanceTransactionSource::ORDER_PAYMENT,
            description: 'Order payment'
        );

        // Add more funds
        $this->balanceService->addFunds(
            user: $user,
            amount: 2000,
            source: BalanceTransactionSource::BONUS,
            description: 'Bonus credit'
        );

        $user->refresh();

        // Check final balance
        $this->assertEquals(900, $user->getBalance()); // 100 - 30 + 20

        // Check transaction history
        $transactions = $user->getRecentBalanceTransactions(10);
        $this->assertCount(3, $transactions);

        // Check transaction order (most recent first)
        $this->assertEquals(BalanceTransactionSource::BONUS, $transactions[0]->source);
        $this->assertEquals(BalanceTransactionSource::ORDER_PAYMENT, $transactions[1]->source);
        $this->assertEquals(BalanceTransactionSource::TOPUP, $transactions[2]->source);

        // Check balance progression
        $this->assertEquals(9000, $transactions[0]->balance_after); // 90.00
        $this->assertEquals(7000, $transactions[1]->balance_after); // 70.00
        $this->assertEquals(10000, $transactions[2]->balance_after); // 100.00
    }

    /** @test */
    public function user_balance_calculation_methods_work_correctly()
    {
        $user = User::factory()->create();

        // Test zero balance
        $this->assertEquals(0, $user->getTotalBalanceCredits());
        $this->assertEquals(0, $user->getTotalBalanceDebits());

        // Add and deduct funds
        $this->balanceService->addFunds($user, 10000, BalanceTransactionSource::TOPUP);
        $this->balanceService->addFunds($user, 5000, BalanceTransactionSource::BONUS);
        $this->balanceService->deductFunds($user, 3000, BalanceTransactionSource::ORDER_PAYMENT);

        $user->refresh();

        // Check totals
        $this->assertEquals(15000, $user->getTotalBalanceCredits());
        $this->assertEquals(3000, $user->getTotalBalanceDebits());
        $this->assertEquals(1200, $user->getBalance());

        // Check formatted balance
        $formattedBalance = app(MoneyService::class)->getFormattedAmount($user->getBalance(), 'RUB');
        $this->assertStringContainsString('120.00', $formattedBalance);
        $this->assertStringContainsString('₽', $formattedBalance);
    }
}
