<?php

use App\Models\Order;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return 'It works ' . now()->format('c');
});

// VPN Subscription Routes
Route::prefix('access')->group(function () {

    Route::any('/{uuid}', [App\Http\Controllers\Access\ProfileController::class, 'show'])
        ->whereUuid('uuid')
        ->name('access.profile');

    // Update routing preference
    Route::post('/{uuid}/settings/routing-preference', [App\Http\Controllers\Access\ProfileController::class, 'updateRoutingPreference'])
        ->whereUuid('uuid')
        ->name('access.routing.preference');

    // Update SNI category
    Route::post('/{uuid}/settings/sni-category', [App\Http\Controllers\Access\ProfileController::class, 'updateSniCategory'])
        ->whereUuid('uuid')
        ->name('access.sni.category');

    // Display available subscription plans + payment methods
    Route::get('/{uuid}/plan/select', [App\Http\Controllers\Access\PlanSelectionController::class, 'show'])
        ->whereUuid('uuid')
        ->name('access.plan.selection');

    // Process plan selection and payment method → generate payment
    Route::post('/{uuid}/plan/purchase', [App\Http\Controllers\Access\VpnPurchaseController::class, 'purchase'])
        ->whereUuid('uuid')
        ->name('access.plan.purchase');
});

// Payment result routes
Route::prefix('orders')->group(function () {
    Route::get('/{orderPublicId}/success', function (string $orderPublicId) {
        $order = Order::findByPublicId($orderPublicId)->firstOrFail();
        return view('access.payment.success', compact('order'));
    })->name('access.payment.success');

    Route::get('/{orderPublicId}/failed', function (string $orderPublicId) {
        $order = Order::findByPublicId($orderPublicId)->firstOrFail();
        return view('access.payment.failed', compact('order'));
    })->name('access.payment.failed');
});

// Payment Webhook Routes (no CSRF protection)
Route::prefix('webhook')->group(function () {
    Route::post('/tbank', [App\Http\Controllers\WebhookController::class, 'tbank'])
        ->name('webhook.tbank');

    Route::post('/{gateway}', [App\Http\Controllers\WebhookController::class, 'handle'])
        ->name('webhook.handle');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

