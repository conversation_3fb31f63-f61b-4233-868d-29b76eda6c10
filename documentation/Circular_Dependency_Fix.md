# Circular Dependency Fix - SmartVPN Balance System

## Problem Description

The SmartVPN application was experiencing segmentation faults (infinite recursion) when accessing the `route('access.plan.purchase', $uuid)` route. The error occurred during the dependency injection phase, before any business logic was executed.

## Root Cause Analysis

The issue was caused by a **circular dependency** in the service container resolution:

### Circular Dependency Chain:
1. `PaymentService::createGateway()` → creates `CombinedPaymentGateway`
2. `CombinedPaymentGateway::__construct()` → calls `app(PaymentService::class)`
3. **INFINITE LOOP**: PaymentService tries to create CombinedPaymentGateway, which tries to get PaymentService, etc.

### Code Location:
```php
// In PaymentService::createGateway()
'combined' => new CombinedPaymentGateway(),

// In CombinedPaymentGateway::__construct()
$this->paymentService = app(PaymentService::class); // ← CIRCULAR DEPENDENCY
```

## Solution Implemented

### 1. **Removed CombinedPaymentGateway from Gateway Registry**
- Removed `'combined' => new CombinedPaymentGateway()` from `PaymentService::createGateway()`
- This prevents the circular dependency during service container resolution

### 2. **Moved Combined Payment Logic to PaymentService**
- Implemented combined payment functionality directly in `PaymentService::createCombinedPayment()`
- This method now handles both balance deduction and external payment creation without using a separate gateway

### 3. **Simplified CombinedPaymentGateway**
- Removed `PaymentService` dependency from `CombinedPaymentGateway` constructor
- Added `createExternalGateway()` method to create external gateways directly
- This gateway is now available for future use if needed, but doesn't cause circular dependencies

### 4. **Updated Payment Method Migration**
- Removed 'combined' payment method from database migration
- Combined payments are now handled programmatically, not as a separate payment method

## Code Changes Summary

### Files Modified:
1. **`app/Services/Payment/Gateways/CombinedPaymentGateway.php`**
   - Removed `PaymentService` dependency
   - Added `createExternalGateway()` method
   - Simplified constructor

2. **`app/Services/PaymentService.php`**
   - Removed CombinedPaymentGateway from gateway registry
   - Rewrote `createCombinedPayment()` method to handle logic directly
   - Removed circular dependency

3. **`database/migrations/2025_07_21_120002_add_balance_payment_methods.php`**
   - Removed 'combined' payment method registration
   - Only registers 'balance' payment method

### New Test:
4. **`tests/Feature/CircularDependencyTest.php`**
   - Tests that services can be instantiated without circular dependencies
   - Verifies that all gateways (except combined) can be created successfully

## How Combined Payments Work Now

### Before (Circular Dependency):
```
PaymentService → CombinedPaymentGateway → PaymentService (LOOP)
```

### After (Direct Implementation):
```
PaymentService::createCombinedPayment() → {
    1. Deduct from balance (BalanceService)
    2. Create external payment (PaymentService::createPayment)
    3. Handle rollback if needed
}
```

## Benefits of This Solution

1. **No Circular Dependencies**: Services can be instantiated without infinite loops
2. **Cleaner Architecture**: Combined payment logic is centralized in PaymentService
3. **Better Error Handling**: Rollback logic is more straightforward
4. **Maintainability**: Fewer moving parts, easier to debug
5. **Performance**: No unnecessary gateway instantiation during service resolution

## Testing

The fix can be verified by:

1. **Running the circular dependency test**:
   ```bash
   php artisan test tests/Feature/CircularDependencyTest.php
   ```

2. **Accessing the problematic route**:
   - The `route('access.plan.purchase', $uuid)` should now work without segmentation faults

3. **Testing balance payments**:
   - Balance-only payments work through `BalancePaymentGateway`
   - Combined payments work through `PaymentService::createCombinedPayment()`

## Future Considerations

- The `CombinedPaymentGateway` class is still available if needed for future enhancements
- Combined payment functionality is now more flexible and can be extended easily
- The architecture supports adding new payment combinations without circular dependencies

This fix resolves the immediate segmentation fault issue while maintaining all balance system functionality and improving the overall architecture.
