# Реферальная система

С помощью представленных методов вы можете получить информацию о рефералах, реферерах вашего контакта.

> **Реферер** – пользователь стоящий выше, тот кто пригласил в реферальную программу.  
> **Реферал** – пользователь стоящий ниже, тот кого пригласили в реферальную программу.

## Получить рефереры контакта

`GET` `https://app.leadteh.ru/api/v1/getReferrers`

Этот метод позволяет получить список или дерево рефереров контакта.

#### Path Parameters

| Name       | Type    | Description                                                                                          |
|------------|---------|--------------------------------------------------------------------------------------------------|
| contact_id*| integer | ID контакта.                                                                                      |
| depth*     | integer | Глубина дерева, мин. 1, макс. 10.                                                                |
| is_flat    | boolean | По умолчанию вы получаете информацию в виде дерева, если укажите значение этого поля в `1`, то информация придет в виде списка рефереров. |


#### `200` Запрос успешно обработан. Результат в виде дерева, включая текущий контакт.

```json
{
  "data": {
    "id": 3,
    "name": "Иван Иванов",
    "messenger": "telegram",
    "created_at": "2019-05-10T10:38:28+00:00",
    "referrer": {
      "id": 2,
      "name": "Петр Петров",
      "messenger": "telegram",
      "created_at": "2019-05-10T10:38:25+00:00",
      "referrer": {
        "id": 1,
        "name": "Василий Васильев",
        "messenger": "telegram",
        "created_at": "2019-05-10T10:11:42+00:00"
      }
    }
  }
}
```

## Получить рефералы контакта

`POST` `https://app.leadteh.ru/api/v1/getReferrals`

Этот метод позволяет получить список рефералов контакта.

#### Path Parameters

| Name        | Type     | Description |
|-------------|----------|-------------|
| filters   | object   | Поля для фильтрации данных. Например фильтр по тегу: <br><br> `{"tag_name": "Горячий"}` <br> `{"tag_name": ["Горячий", "Холодный"]}` <br> `{"tag_id": 1}` <br> `{"tag_id": [1, 2]}` |
| page      | integer  | Номер страницы результатов. |
| contact_id* | integer  | ID контакта. |

_* — обязательные поля_

#### `200`

```json
// пустой ответ
```

## Получить количество рефералов всей сети контакта

`POST` `https://app.leadteh.ru/api/v1/getCountReferrals`

Этот метод позволяет получить количество рефералов всей сети контакта.

### Path Parameters

| Name       | Type    | Description |
|------------|---------|-------------|
| filters  | object  | Поля для фильтрации данных. Например фильтр по тегу:<br><br>`{"tag_name": "Горячий"}`<br>`{"tag_name": ["Горячий", "Холодный"]}`<br>`{"tag_id": 1}`<br>`{"tag_id": [1, 2]}` |
| contact_id* | integer | ID контакта. |

_* — обязательные поля_

#### `200`

```json
// пустой ответ
```

> ℹ️ **Примечание:**  
> Если вы указываете фильтр в запросе, то он прописывается в таком виде:  
> `filters[tag_name]=Горячий`
