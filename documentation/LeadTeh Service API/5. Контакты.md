# Контакты

## Получить список контактов

`GET` `https://app.leadteh.ru/api/v1/getContacts`

Этот метод позволяет получить список контактов указанного бота.

#### Path Parameters

| Name     | Type    | Description                                                                                         |
|----------|---------|-------------------------------------------------------------------------------------------------|
| date_from| integer | Фильтр по дате создания контакта в формате Unix Time                                            |
| date_to  | integer | Фильтр по дате создания контакта в формате Unix Time                                            |
| count    | integer | Количество контактов для получения. Максимальное значение: 500                                  |
| with     | string  | Список дополнительных сущностей для контакта, передается в виде перечисления через запятую. Пример: `tags,variables` |
| bot_id   | integer | ID бота.                                                                                         |

---

#### `200` Запрос успешно обработан.

```json
{
  "data": [
    {
      "id": 1,
      "bot_id": 1,
      "phone": "79991234567",
      "email": "<EMAIL>",
      "name": "Иван Иванов",
      "messenger": "whatsapp",
      "address": "г. Москва, ул. Пушкина, д. 5",
      "utm": {
        "utm_source": "landing",
        "utm_medium": "...",
        "utm_campaign": "...",
        "utm_term": "...",
        "utm_content": "..."
      },
      "created_at": "2019-05-10T10:38:28+00:00"
    },
    {
      "id": 2,
      "bot_id": 1,
      "phone": "792712312321",
      "email": null,
      "name": "Петр Петров",
      "messenger": "telegram",
      "telegram_id": "123456",
      "telegram_username": "superman",
      "address": null,
      "utm": null,
      "created_at": "2019-04-02T12:16:16+00:00"
    },
    {
      "id": 3,
      "bot_id": 2,
      "phone": null,
      "email": null,
      "name": "Василий Васильев",
      "messenger": "viber",
      "viber_id": "1123456789dD20=",
      "address": null,
      "utm": null,
      "created_at": "2019-05-11T15:31:34+00:00"
    },
  ],
  "links": {
    "first": "http:\/\/leadteh.ru\/api\/v1\/getContacts?page=1",
    "last": "http:\/\/leadteh.ru\/api\/v1\/getContacts?page=1",
    "prev": null,
    "next": null
  },
  "meta": {
    "current_page": 1,
    "from": 1,
    "last_page": 1,
    "path": "http:\/\/leadteh.ru\/api\/v1\/getContacts",
    "per_page": 500,
    "to": 3,
    "total": 3
  }
}
```

## Создать или обновить контакт

`POST` `https://app.leadteh.ru/api/v1/createOrUpdateContact`

Этот метод позволяет создать или обновить контакт указанного бота.

#### Path Parameters

| Name             | Type    | Description                                                                                           |
|------------------|---------|---------------------------------------------------------------------------------------------------|
| bot_id*          | integer | ID бота                                                                                             |
| messenger*       | string  | Тип мессенджера. Возможные значения: `whatsapp`, `telegram`, `viber`, `vk`, `facebook`, `instagram`, `icq` |
| name*            | string  | Имя контакта                                                                                       |
| phone            | string  | Номер телефона контакта в международном формате (+79991234567). Обязателен, когда `messenger == "whatsapp"` |
| telegram_id      | int     | ID пользователя в Телеграм. Обязателен, когда `messenger == "telegram"`                            |
| telegram_username| string  | Username пользователя Телеграм                                                                    |
| viber_id         | string  | ID пользователя в Viber. Обязателен, когда `messenger == "viber"`                                 |
| vk_id            | int     | ID пользователя в ВКонтакте. Обязателен, когда `messenger == "vk"`                                |
| fb_id            | int     | ID пользователя в Facebook. Обязателен, когда `messenger == "facebook"`                           |
| instagram_id     | int     | ID пользователя в Instagram. Обязателен, когда `messenger == "instagram"`                         |
| icq_id           | int     | ID пользователя в ICQ. Обязателен, когда `messenger == "icq"`                                    |
| icq_nick         | string  | Username пользователя ICQ                                                                          |
| email            | string  | Email контакта                                                                                     |
| address          | string  | Адрес контакта                                                                                    |
| tags             | array   | Массив тегов контакта. Пример: `["Тег 1", "Тег 2"]`                                              |

---

#### `200`: OK Запрос успешно обработан.

```json
{
  "data": {
      "id": 1,
      "bot_id": 1,
      "phone": "79991234567",
      "email": "<EMAIL>",
      "name": "Ivan Ivanov",
      "address": "Moscow",
      "messenger": "whatsapp",
      "utm": [],
      "avatar": null,
      "telegram_id": null,
      "telegram_username": null,
      "vk_user_id": null,
      "viber_id": null,
      "created_at": "2019-05-10T10:38:28+00:00",
      "unsubscribed_at": null,
      "tags": ["Тег 1", "Тег 2"],
      "variables": []
  }
}
```

Вот список дополнительных сущностей для контакта:

* `tags` — список тегов
* `variables` — список пользовательских переменных
