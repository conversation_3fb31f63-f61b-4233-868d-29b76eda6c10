# Пользовательские переменные

## Получить список переменных

`GET` `https://app.leadteh.ru/api/v1/getContactVariables`

Этот метод позволяет получить список пользовательских переменных для определенного контакта.

#### Path Parameters

| Name        | Type   | Description   |
|-------------|--------|----------------|
| contact_id* | string | ID контакта. |

_* — обязательные поля_

#### `200` Запрос успешно обработан.

```json
{
  "data": [
    {
      "id": 1,
      "value": "Значение переменной",
      "created_at": "2019-10-22T20:54:20+00:00",
      "updated_at": "2019-10-22T20:54:31+00:00",
      "variable": {
        "id": 14,
        "name": "Имя переменной",
        "created_at": "2019-10-22T20:54:20+00:00",
        "updated_at": "2019-10-22T20:54:20+00:00"
      }
    },
    {
      "id": 2,
      "value": "Москва",
      "created_at": "2019-10-22T20:54:20+00:00",
      "updated_at": "2019-10-22T20:54:31+00:00",
      "variable": {
        "id": 25,
        "name": "Город",
        "created_at": "2019-10-22T20:54:20+00:00",
        "updated_at": "2019-10-22T20:54:20+00:00"
      }
    }
  ],
  "links": {
    "first": "https:\/\/leadteh.ru\/api\/v1\/getContactVariables?page=1",
    "last": "https:\/\/leadteh.ru\/api\/v1\/getContactVariables?page=1",
    "prev": null,
    "next": null
  },
  "meta": {
    "current_page": 1,
    "from": 1,
    "last_page": 1,
    "path": "https:\/\/leadteh.ru\/api\/v1\/getContactVariables",
    "per_page": 100,
    "to": 2,
    "total": 2
  }
}
```

#### `422` Переданные данные некорректны.

```json
{
  "message": "The given data was invalid.",
  "errors": {
    "contact_id": [
      "Поле contact id обязательно для заполнения."
    ]
  }
}
```

## Создать/обновить переменную

`POST` `https://app.leadteh.ru/api/v1/setContactVariable`

Этот метод позволяет создать или обновить пользовательскую переменную. Если такой переменной не существует в боте, то она создастся и привяжется к пользователю.

### Query Parameters

| Name         | Type    | Description                                                                                                                                     |
|--------------|---------|-------------------------------------------------------------------------------------------------------------------------------------------------|
| `contact_id` | integer | ID контакта.                                                                                                                                     |
| `name`       | string  | Имя переменной.                                                                                                                                  |
| `value`      | string  | Значение переменной.                                                                                                                             |
| `deletable`  | integer | Возможные значения:  
|              |         | `0` — переменная не должна удалиться после заявки  
|              |         | `1` — переменная должна удалиться после заявки  
|              |         | По умолчанию: `0`                                                                                                                                |

#### `200` Переменная успешно создана/отредактирована.

```json
{
  "data": {
    "id": 2,
    "value": "Значение переменной",
    "created_at": "2019-10-23T11:46:15+00:00",
    "updated_at": "2019-10-23T11:46:28+00:00",
    "variable": {
      "id": 1,
      "name": "Имя переменной",
      "created_at": "2019-10-23T11:46:15+00:00",
      "updated_at": "2019-10-23T11:46:15+00:00"
    }
  }
}
```

#### `403` Доступ запрещен.

```json
{
    "message": "Forbidden"
}
```

#### `422` Переданы некорректные данные.

```json
{
  "message": "The given data was invalid.",
  "errors": {
    "contact_id": [
      "Поле contact id обязательно для заполнения."
    ],
    "name": [
      "Поле Имя обязательно для заполнения."
    ],
    "value": [
      "Поле value обязательно для заполнения."
    ]
  }
}
```
