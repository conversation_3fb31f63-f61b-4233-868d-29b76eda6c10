# Основы

Для доступа к API необходимо подключить соответствующий тарифный план.

В каждом запросе API необходимо отправлять токен в GET-параметре `api_token`.  
Токен вы можете получить в разделе:  
[https://app.leadteh.ru/settings/apikey](https://app.leadteh.ru/settings/apikey)

Пример запроса:
```

[https://app.leadteh.ru/api/v1/method?api\_token=Ваш](https://app.leadteh.ru/api/v1/method?api_token=Ваш) API token

```

Также необходимо передавать следующий заголовок:
```

X-Requested-With\:XMLHttpRequest

```

## Лимиты

К API сервиса LeadTex разрешено делать не более **2 запросов в секунду** (120 запросов в минуту).  
Иначе API будет возвращать ошибку `429 Too Many Requests`.  
Также на некоторые методы могут накладываться свои ограничения.

> ❗ **Важно:**  
> Запрещено хранить токен в открытом виде, например в клиентских приложениях, где любой желающий может посмотреть этот токен.
