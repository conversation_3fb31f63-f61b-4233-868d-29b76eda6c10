# SmartVPN Balance System - Usage Guide

## Overview

The SmartVPN balance system allows users to maintain an account balance and use it for payments. The system supports:

- Balance top-ups via external payment gateways
- Balance-only payments for orders
- Combined payments (balance + external gateway)
- Full transaction history and audit trail
- Negative balance support with configurable limits

## Key Components

### 1. Models

- **User**: Extended with balance-related methods
- **BalanceTransaction**: Records all balance changes
- **Order**: Enhanced with balance payment tracking

### 2. Services

- **BalanceService**: Core balance operations
- **OrderService**: Extended with balance payment methods
- **PaymentService**: Integrated with balance gateways

### 3. Payment Gateways

- **BalancePaymentGateway**: Handles balance-only payments
- **CombinedPaymentGateway**: Handles balance + external payments

## Basic Usage

### Getting User Balance

```php
$user = Auth::user();

// Get balance in major currency units (rubles, dollars)
$balance = $user->getBalance(); // e.g., 150.75

// Get balance in minor currency units (kopecks, cents)
$balanceMinor = $user->getTotalBalanceAmount(); // e.g., 15075

// Check if user can afford an amount
$canAfford = $user->canAfford(100.0); // true/false

// Get formatted balance for display
$formatted = $user->getFormattedBalance(); // "150.75 ₽"
```

### Adding Funds to Balance

```php
use App\Services\BalanceService;
use App\Enums\BalanceTransactionSource;

$balanceService = app(BalanceService::class);

$transaction = $balanceService->addFunds(
    user: $user,
    amount: 100.50, // Major currency units
    source: BalanceTransactionSource::TOPUP,
    description: 'Balance top-up via TBank',
    order: $order, // Optional
    payment: $payment // Optional
);
```

### Deducting Funds from Balance

```php
$transaction = $balanceService->deductFunds(
    user: $user,
    amount: 50.25,
    source: BalanceTransactionSource::ORDER_PAYMENT,
    description: 'Payment for VPN subscription',
    order: $order
);
```

### Creating Balance Top-up Orders

```php
use App\Services\OrderService;

$orderService = app(OrderService::class);

$order = $orderService->createBalanceTopUpOrder(
    user: $user,
    amount: 100.0,
    currency: 'RUB',
    description: 'Balance top-up'
);
```

### Processing Balance-Only Payments

```php
// Check if user can afford the order
if (!$user->canAfford($order->total_amount / 100)) {
    throw new Exception('Insufficient balance');
}

// Process the payment
$success = $orderService->processBalanceOnlyPayment($order);
```

### Processing Combined Payments

```php
use App\Services\PaymentService;

$paymentService = app(PaymentService::class);

$result = $paymentService->createCombinedPayment(
    order: $order,
    balanceAmount: 50.0, // Amount from balance
    externalMethod: 'tbank', // External payment method
    additionalData: [] // Additional data for external gateway
);
```

## Transaction History

### Getting User Transaction History

```php
// Get recent transactions
$recentTransactions = $user->getRecentBalanceTransactions(10);

// Get paginated transaction history
$transactions = $user->getBalanceTransactionHistory(20);

// Get transaction totals
$totalCredits = $user->getTotalBalanceCredits();
$totalDebits = $user->getTotalBalanceDebits();
```

### Transaction Properties

```php
$transaction = $user->balanceTransactions()->first();

// Basic properties
$transaction->type; // BalanceTransactionType::CREDIT or DEBIT
$transaction->source; // BalanceTransactionSource enum
$transaction->amount; // Amount in minor units
$transaction->balance_before; // Balance before transaction
$transaction->balance_after; // Balance after transaction
$transaction->description; // Optional description

// Relationships
$transaction->user; // User who owns the transaction
$transaction->order; // Related order (if any)
$transaction->payment; // Related payment (if any)

// Helper methods
$transaction->isCredit(); // true/false
$transaction->isDebit(); // true/false
$transaction->getAmountInMajorUnits(); // Amount in major currency units
```

## Order Payment Tracking

### Order Payment Methods

```php
$order = Order::find($id);

// Check payment status
$order->isFullyPaid(); // true/false
$order->getTotalPaidAmount(); // Total amount paid (balance + external)
$order->getRemainingAmount(); // Amount still needed

// Get payment breakdown
$breakdown = $order->getPaymentBreakdown();
// Returns:
// [
//     'total_amount' => 100.0,
//     'balance_used' => 50.0,
//     'external_payments' => 50.0,
//     'total_paid' => 100.0,
//     'remaining_amount' => 0.0,
//     'is_fully_paid' => true,
//     'payment_type' => 'combined'
// ]

// Check payment type
$order->isBalanceOnlyPayment(); // true/false
$order->isCombinedPayment(); // true/false
$order->getPaymentType(); // 'balance_only', 'combined', 'external_only', 'unpaid'
```

## Events and Listeners

The system automatically handles balance crediting for top-up orders:

- When a balance top-up order is paid, the `CreditBalanceOnTopUpOrderPaid` listener automatically credits the user's balance
- All balance transactions trigger the `BalanceTransactionCreated` event for logging and notifications

## Configuration

### Overdraft Limits

The system allows negative balances up to a configurable limit (default: -10,000 in major currency units):

```php
// In BalanceService and User model
$maxOverdraft = -1000000; // -10,000 in minor units
```

### Payment Methods

Balance and combined payment methods are automatically added via migration:

- `balance`: For balance-only payments
- `combined`: For balance + external payments

## Error Handling

Common exceptions and how to handle them:

```php
try {
    $balanceService->deductFunds($user, $amount, $source);
} catch (\InvalidArgumentException $e) {
    // Invalid amount (negative or zero)
} catch (\Exception $e) {
    // Insufficient balance or other errors
    if (str_contains($e->getMessage(), 'Insufficient balance')) {
        // Handle insufficient balance
    }
}
```

## Best Practices

1. **Always use transactions** for balance operations to ensure consistency
2. **Check balance before deducting** to provide better user experience
3. **Use appropriate transaction sources** for proper categorization
4. **Include descriptive messages** for better audit trails
5. **Handle exceptions gracefully** and provide meaningful error messages to users
6. **Use events and listeners** for automatic balance processing
7. **Test thoroughly** with various scenarios including edge cases

## Testing

Run the balance system tests:

```bash
php artisan test tests/Feature/BalanceSystemTest.php
```

The test suite covers:
- Basic balance operations
- Order creation and payment processing
- Transaction history tracking
- Error handling scenarios
- Balance calculation methods
