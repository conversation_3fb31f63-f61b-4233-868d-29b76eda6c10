<?php

namespace App\Console\Commands;

use App\Services\Leadteh\LeadtehService;
use App\Exceptions\Leadteh\LeadtehApiException;
use App\Exceptions\Leadteh\LeadtehRateLimitException;
use App\Exceptions\Leadteh\LeadtehAccountException;
use Illuminate\Console\Command;
use Exception;
use Illuminate\Support\Str;

class TestLeadtehService extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'leadteh:test
                            {contact_id=********}
                            {--method=all}
                            {--reward}
                            {--reward-rollback}
                            {--reward-amount=10000}';

    /**
     * The console command description.
     */
    protected $description = 'Test LeadTeh Service API methods';

    private LeadtehService $leadtehService;
    private int $contactId;

    private string $testVar;
    private string $testVal;
    private string $testTagName;
    private ?string $testAccountId;

    public function __construct()
    {
        parent::__construct();

        $this->testVar = 'laravel_test_var';
        $this->testVal = now()->format('c');
        $this->testTagName = 'laravel_test_tag';
        $this->testAccountId = null;
    }

    private function setTestAccountId(int|string $testAccountId): void
    {
        $this->testAccountId = $testAccountId;
    }

    private function getTestAccountId(): int|string
    {
        return $this->testAccountId;
    }

    private function isSetTestAccountId(): bool
    {
        return $this->testAccountId !== null;
    }


    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        // dd($this->options());
        $this->contactId = (int) $this->argument('contact_id');
        $method = $this->option('method');

        $reward = $this->option('reward');
        $rewardRollback = $this->option('reward-rollback');
        $rewardAmount = (int) $this->option('reward-amount');

        $reasonText = 'Laravel Test reason';
        $testAdditionalData = [
            'payment_id' => 'PAY-TEST-' .Str::upper(Str::random(7)),
            'payment_created_at' => now()->subWeek()->format('c'), // минус неделя
        ];

        try {
            $this->leadtehService = new LeadtehService();
        } catch (Exception $e) {
            $this->error('Failed to initialize LeadTeh Service: ' . $e->getMessage());
            return 1;
        }

        $this->info("Testing LeadTeh Service with contact ID: {$this->contactId}");
        $this->newLine();

        if ($reward) {

            try {
                $result = $this->leadtehService->processReferralRewards($rewardAmount, $this->contactId, $testAdditionalData);
                $this->displayResult($result);
            } catch (Exception $e) {
                $this->error('Failed to process referral rewards: ' . $e->getMessage());
            }
            return 0;
        }

        if ($rewardRollback) {
            try {
                $result = $this->leadtehService->rollbackReferralRewards($rewardAmount, $this->contactId, $testAdditionalData, $reasonText);
                $this->displayResult($result);
            } catch (Exception $e) {
                $this->error('Failed to rollback referral rewards: ' . $e->getMessage());
            }
            return 0;
        }

        if ($method === 'all') {
            $this->testAllMethods();
        } else {
            $this->testSpecificMethod($method);
        }

        return 0;
    }

    private function testAllMethods(): void
    {
        $methods = [
            'getMe' => 'Test getMe method',
            'getContacts' => 'Test getContacts method',
            'sendMessage' => 'Test sendMessage method',
            'setContactVariable' => 'Test setContactVariable method',
            'checkContactVariable' => 'Check the variable is set',
            'getContactVariables' => 'Test getContactVariables method',
            'deleteContactVariable' => 'Test deleteContactVariable method',
            'checkContactVariableDeleted' => 'Check the variable is deleted',
            'getReferrers' => 'Test getReferrers method',
            'getReferrals' => 'Test getReferrals method',
            'getCountReferrals' => 'Test getCountReferrals method',
            'attachTagToContact' => 'Test attachTagToContact method',
            'checkContactTags' => 'Check the tag is set',
            'detachTagFromContact' => 'Test detachTagFromContact method',
            'getContactTags' => 'Test getContactTags method',
            'addContactAccount' => 'Test addContactAccount method',
            'getContactAccounts' => 'Test getContactAccounts method #1',
            'addFundsToContactAccount' => 'Test addFundsToContactAccount method',
            'getContactAccounts' => 'Test getContactAccounts method #2',
            'withdrawFundsFromContactAccount' => 'Test withdrawFundsFromContactAccount method',
            'getContactAccounts' => 'Test getContactAccounts method #3',
            'deleteContactAccount' => 'Test deleteContactAccount method',
            'getContactAccounts' => 'Test getContactAccounts method #4',
        ];

        foreach ($methods as $method => $description) {
            $this->info($description);

            $this->testMethod($method);

            $this->newLine();

            // Rate limiting delay
            sleep(1);
        }
    }

    private function testSpecificMethod(string $method): void
    {
        $this->info("Testing specific method: {$method}");
        $this->testMethod($method);
    }

    private function testMethod(string $method): void
    {
        try {
            switch ($method) {
                case 'getMe':
                    $result = $this->leadtehService->getMe();
                    $this->displayResult($result);
                    break;

                case 'getContacts':
                    $result = $this->leadtehService->getContacts(['count' => 5]);
                    // only id, name fields
                    $contacts = collect($result['data'])->map(fn ($contact) => [
                        'id' => $contact['id'],
                        'name' => $contact['name'],
                    ]);
                    $this->displayResult($contacts);
                    break;

                case 'getContactTags':
                    $result = $this->leadtehService->getContactTags($this->contactId);
                    $this->displayResult($result);
                    break;

                case 'setContactVariable':
                    $result = $this->leadtehService->setContactVariable(
                        $this->contactId,
                        $this->testVar,
                        $this->testVal,
                    );
                    $this->displayResult($result);
                    break;

                case 'checkContactTags':
                    $result = $this->leadtehService->getContactTags($this->contactId);
                    if (in_array($this->testTagName, $result['data'])) {
                        $this->alert("Tag found");
                    } else {
                        $this->error("Tag not found");
                    }
                    $this->displayResult($result);
                    break;

                case 'getContactVariables':
                    $result = $this->leadtehService->getContactVariables($this->contactId);
                    // find variable by name
                    $variable = collect($result['data'])->firstWhere('name', $this->testVar);
                    if ($variable) {
                        $this->alert("Found variable: " . json_encode($variable, JSON_PRETTY_PRINT));
                    } else {
                        $this->error("Variable not found");
                    }
                    $this->displayResult($result);
                    break;

                case 'checkContactVariable':
                    $result = $this->leadtehService->getContactVariables($this->contactId);
                    // find variable by name
                    $variable = collect($result['data'])->firstWhere('name', $this->testVar);
                    if ($variable) {
                        $this->alert("Found variable: " . json_encode($variable, JSON_PRETTY_PRINT));
                    } else {
                        $this->error("Variable not found");
                    }
                    $this->displayResult($result);
                    break;

                case 'deleteContactVariable':
                    $result = $this->leadtehService->deleteContactVariable(
                        $this->contactId,
                        $this->testVar
                    );
                    $this->displayResult($result);
                    break;

                case 'checkContactVariableDeleted':
                    $result = $this->leadtehService->getContactVariables($this->contactId);
                    // find variable by name
                    $variable = collect($result['data'])->firstWhere('name', $this->testVar);
                    if ($variable) {
                        $this->error("Variable not deleted");
                    } else {
                        $this->alert("Variable deleted");
                    }
                    $this->displayResult($result);
                    break;

                case 'getContactAccounts':
                    $result = $this->leadtehService->getContactAccounts($this->contactId);
                    $this->displayResult($result);
                    break;

                case 'getReferrers':
                    $result = $this->leadtehService->getReferrers($this->contactId, 3);
                    // replace referrer field's value by count of referrers
                    $result['data']['referrer'] = 'Count: ' . count($result['data']['referrer']);
                    $this->displayResult($result);
                    break;

                case 'attachTagToContact':
                    $result = $this->leadtehService->attachTagToContact(
                        $this->contactId,
                        'test'
                    );
                    $this->displayResult($result);
                    break;

                case 'sendMessage':
                    $result = $this->leadtehService->sendMessage(
                        $this->contactId,
                        'Test message from LeadTeh Service test command at ' . now()->toDateTimeString()
                    );
                    $this->displayResult($result);
                    break;

                case 'getReferrals':
                    $result = $this->leadtehService->getReferrals($this->contactId);
                    $this->displayResult($result);
                    break;

                case 'getCountReferrals':
                    $result = $this->leadtehService->getCountReferrals($this->contactId);
                    $this->displayResult($result);
                    break;

                case 'detachTagFromContact':
                    $result = $this->leadtehService->detachTagFromContact(
                        $this->contactId,
                        $this->testTagName
                    );
                    $this->displayResult($result);
                    break;

                case 'addContactAccount':
                    if ($this->isSetTestAccountId()) {
                        $this->error('Account already created. Please delete it first.');
                        break;
                    }
                    $result = $this->leadtehService->addContactAccount($this->contactId, 'RUB');
                    $this->setTestAccountId($result['data']['id']);
                    // if not set
                    if (!$this->isSetTestAccountId()) {
                        $this->error('Account ID not set. Please check the code.');
                        break;
                    }
                    $this->alert("Account created. ID: " . $this->getTestAccountId());
                    $this->displayResult($result);
                    break;

                case 'deleteContactAccount':
                    if (!$this->isSetTestAccountId()) {
                        $this->error('Account not created. Please create it first.');
                        break;
                    }
                    $result = $this->leadtehService->deleteContactAccount($this->getTestAccountId());
                    $this->displayResult($result);
                    break;

                case 'addFundsToContactAccount':
                    if (!$this->isSetTestAccountId()) {
                        $this->error('Account not created. Please create it first.');
                        break;
                    }
                    $result = $this->leadtehService->addFundsToContactAccount(
                        $this->getTestAccountId(),
                        1000,
                        'Test deposit'
                    );
                    $this->displayResult($result);
                    break;

                case 'withdrawFundsFromContactAccount':
                    if (!$this->isSetTestAccountId()) {
                        $this->error('Account not created. Please create it first.');
                        break;
                    }
                    $result = $this->leadtehService->withdrawFundsFromContactAccount(
                        $this->getTestAccountId(),
                        1000,
                        'Test withdrawal'
                    );
                    $this->displayResult($result);
                    break;

                default:
                    $this->error("Unknown method: {$method}");
                    break;
            }
        } catch (LeadtehRateLimitException $e) {
            $this->error('Rate limit exceeded. Please wait and try again.');
        } catch (LeadtehApiException $e) {
            $this->error('API Error: ' . $e->getMessage());
            if ($e->getApiResponse()) {
                $this->line('Response data: ' . json_encode($e->getApiResponse(), JSON_PRETTY_PRINT));
            }
        } catch (LeadtehAccountException $e) {
            $this->error('Account Error: ' . $e->getMessage());
        } catch (Exception $e) {
            $this->error('Unexpected error: ' . $e->getMessage());
        }
    }

    private function displayResult(mixed $result): void
    {
        $this->line('<fg=green>Success!</>');
        if (is_array($result)) {
            $this->line('Response: ' . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        } else {
            $this->alert('Response: ' . var_export($result, true));
        }
    }
}
