<?php

namespace App\Console\Commands;

use App\Services\XuiServerSyncService;
use Illuminate\Console\Command;

class SyncXuiServers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'xui:sync-servers
                            {--ignore-confirmation : Ignore confirmation prompt}
                            {--async : Run synchronization asynchronously using jobs}
                            {--batch-size=10 : Number of servers to process concurrently}
                            {--server-ids= : Comma-separated list of server IDs to sync}
                            {--force : Sync all servers regardless of auto_sync setting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize XUI servers data including status, settings, inbounds, and online clients';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Starting XUI Server Synchronization');

        // Parse options
        $ignoreConfirmation = ! $this->option('ignore-confirmation');
        $async = $this->option('async');
        $batchSize = (int) $this->option('batch-size');
        $serverIdsOption = $this->option('server-ids');
        $force = $this->option('force');

        // Validate batch size
        if ($batchSize < 1 || $batchSize > 50) {
            $this->error('Batch size must be between 1 and 50');
            return 1;
        }

        // Parse server IDs
        $serverIds = null;
        if ($serverIdsOption) {
            $serverIds = array_map('intval', explode(',', $serverIdsOption));
            $serverIds = array_filter($serverIds, fn($id) => $id > 0);

            if (empty($serverIds)) {
                $this->error('Invalid server IDs provided');
                return 1;
            }
        }

        // Display configuration
        $this->table(['Option', 'Value'], [
            ['Async Mode', $async ? 'Yes' : 'No'],
            ['Batch Size', $batchSize],
            ['Server IDs', $serverIds ? implode(', ', $serverIds) : 'All eligible servers'],
            ['Force Sync', $force ? 'Yes' : 'No'],
        ]);

        if ($ignoreConfirmation) {
            if (!$this->confirm('Do you want to proceed with the synchronization?')) {
                $this->info('Synchronization cancelled');
                return 0;
            }
        } else {
            $this->info('Confirmation ignored, proceeding with synchronization');
        }

        // Execute synchronization
        try {
            $syncService = app(XuiServerSyncService::class);

            $results = $syncService->syncServers([
                'async' => $async,
                'batch_size' => $batchSize,
                'server_ids' => $serverIds,
                'force' => $force,
            ]);

            // Display results
            $this->info('✅ Synchronization completed!');

            if ($async) {
                $this->table(['Metric', 'Count'], [
                    ['Jobs Dispatched', $results['jobs_dispatched'] ?? 0],
                ]);
                $this->info('💡 Check the queue workers and logs for job execution status');
            } else {
                $this->table(['Metric', 'Count'], [
                    ['Synced Successfully', $results['synced'] ?? 0],
                    ['Failed', $results['failed'] ?? 0],
                    ['Skipped', $results['skipped'] ?? 0],
                ]);
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Synchronization failed: {$e->getMessage()}");

            if ($this->option('verbose')) {
                $this->error($e->getTraceAsString());
            }

            return 1;
        }
    }
}
