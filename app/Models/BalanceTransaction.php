<?php

namespace App\Models;

use App\Enums\BalanceTransactionSource;
use App\Enums\BalanceTransactionType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BalanceTransaction extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'type',
        'source',
        'amount',
        'currency',
        'balance_before',
        'balance_after',
        'description',
        'order_id',
        'payment_id',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'type' => BalanceTransactionType::class,
            'source' => BalanceTransactionSource::class,
            'amount' => 'integer',
            'balance_before' => 'integer',
            'balance_after' => 'integer',
        ];
    }

    /* ---------------- Relationships ---------------- */

    /**
     * Get the user that owns this transaction.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the order associated with this transaction.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the payment associated with this transaction.
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    /* ---------------- Accessors & Mutators ---------------- */

    /**
     * Get the amount in major currency units (e.g., rubles, dollars).
     */
    public function getAmountInMajorUnitsAttribute(): float
    {
        return $this->amount / 100;
    }

    /**
     * Get the balance before in major currency units.
     */
    public function getBalanceBeforeInMajorUnitsAttribute(): float
    {
        return $this->balance_before / 100;
    }

    /**
     * Get the balance after in major currency units.
     */
    public function getBalanceAfterInMajorUnitsAttribute(): float
    {
        return $this->balance_after / 100;
    }

    /* ---------------- State Checks ---------------- */

    /**
     * Check if this is a credit transaction.
     */
    public function isCredit(): bool
    {
        return $this->type === BalanceTransactionType::CREDIT;
    }

    /**
     * Check if this is a debit transaction.
     */
    public function isDebit(): bool
    {
        return $this->type === BalanceTransactionType::DEBIT;
    }

    /**
     * Check if this transaction is related to an order.
     */
    public function hasOrder(): bool
    {
        return $this->order_id !== null;
    }

    /**
     * Check if this transaction is related to a payment.
     */
    public function hasPayment(): bool
    {
        return $this->payment_id !== null;
    }

    /* ---------------- Scopes ---------------- */

    /**
     * Scope to filter by transaction type.
     */
    public function scopeOfType($query, BalanceTransactionType $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by transaction source.
     */
    public function scopeOfSource($query, BalanceTransactionSource $source)
    {
        return $query->where('source', $source);
    }

    /**
     * Scope to filter credit transactions.
     */
    public function scopeCredits($query)
    {
        return $query->where('type', BalanceTransactionType::CREDIT);
    }

    /**
     * Scope to filter debit transactions.
     */
    public function scopeDebits($query)
    {
        return $query->where('type', BalanceTransactionType::DEBIT);
    }

    /**
     * Scope to order by most recent first.
     */
    public function scopeRecent($query)
    {
        return $query->orderBy('created_at', 'desc');
    }
}
