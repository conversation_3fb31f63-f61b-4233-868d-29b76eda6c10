<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SniCategory extends Model
{
    protected $fillable = [
        'name',
        'label',
        'enabled',
    ];

    protected $casts = [
        'enabled' => 'boolean',
    ];

    public function providers(): HasMany
    {
        return $this->hasMany(SniProvider::class, 'category_id');
    }

    public function enabledProviders(): HasMany
    {
        return $this->providers()->where('enabled', true);
    }

    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }
}