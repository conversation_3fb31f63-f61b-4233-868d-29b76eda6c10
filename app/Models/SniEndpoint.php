<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class SniEndpoint extends Model
{
    protected $fillable = [
        'provider_id',
        'domain',
        'name',
        'enabled',
        'accessible',
        'tls_valid',
        'ping',
        'last_ping_at',
        'last_checked_at',
        'country',
        'cdn_provider',
        'priority',
        'verified',
        'used_times',
        'source_ip',
        'notes',
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'accessible' => 'boolean',
        'tls_valid' => 'boolean',
        'verified' => 'boolean',
        'ping' => 'integer',
        'priority' => 'integer',
        'used_times' => 'integer',
        'last_ping_at' => 'datetime',
        'last_checked_at' => 'datetime',
    ];

    public function provider(): BelongsTo
    {
        return $this->belongsTo(SniProvider::class, 'provider_id');
    }

    public function sniTags(): BelongsToMany
    {
        return $this->belongsToMany(SniTag::class, 'sni_endpoint_tag');
    }

    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }

    public function scopeAccessible($query)
    {
        return $query->where('accessible', true);
    }

    public function scopeVerified($query)
    {
        return $query->where('verified', true);
    }

    public function scopeByCountry($query, string $country)
    {
        return $query->where('country', $country);
    }

    public function scopeOrderByPriority($query)
    {
        return $query->orderBy('priority');
    }

    public function scopeOrderByPing($query)
    {
        return $query->orderBy('ping');
    }

    public function incrementUsage(): void
    {
        $this->increment('used_times');
    }
}
