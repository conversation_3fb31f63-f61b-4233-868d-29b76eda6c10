<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentMethod extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'code',
        'short_code',
        'name',
        'type',
        'is_active',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    public const TYPE_ONLINE = 'online';
    public const TYPE_OFFLINE = 'offline';
    public const TYPE_MANUAL = 'manual';

    public const TYPES = [
        self::TYPE_ONLINE,
        self::TYPE_OFFLINE,
        self::TYPE_MANUAL,
    ];

    public const CODE_CASH = 'cash';
    public const CODE_MANUAL = 'manual';
    public const CODE_FREE = 'free';
    public const CODE_BALANCE = 'balance';
    public const CODE_T_BANK = 'tbank';
    public const CODE_COMBINED = 'combined';

    public const CODES = [
        self::CODE_CASH,
        self::CODE_MANUAL,
        self::CODE_FREE,
        self::CODE_BALANCE,
        self::CODE_T_BANK,
        self::CODE_COMBINED,
    ];

    /**
     * Get the payments using this method.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'method_id');
    }

    /**
     * Get the payment webhooks for this method.
     */
    public function webhooks(): HasMany
    {
        return $this->hasMany(PaymentWebhook::class, 'method_id');
    }

    /**
     * Scope to get only active payment methods.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get online payment methods.
     */
    public function scopeOnline($query)
    {
        return $query->where('type', 'online');
    }

    /**
     * Scope to get offline payment methods.
     */
    public function scopeOffline($query)
    {
        return $query->where('type', 'offline');
    }

    /**
     * Scope to get manual payment methods.
     */
    public function scopeManual($query)
    {
        return $query->where('type', 'manual');
    }

    /**
     * Check if this is an online payment method.
     */
    public function isOnline(): bool
    {
        return $this->type === 'online';
    }

    /**
     * Check if this is an offline payment method.
     */
    public function isOffline(): bool
    {
        return $this->type === 'offline';
    }

    /**
     * Check if this is a manual payment method.
     */
    public function isManual(): bool
    {
        return $this->type === 'manual';
    }

    /**
     * Get the total number of successful payments.
     */
    public function getSuccessfulPaymentsCount(): int
    {
        return $this->payments()->where('status', 'paid')->count();
    }

    /**
     * Get the total amount of successful payments.
     */
    public function getTotalSuccessfulAmount(): int
    {
        return $this->payments()->where('status', 'paid')->sum('amount');
    }

    /**
     * Get the success rate percentage.
     */
    public function getSuccessRate(): float
    {
        $totalPayments = $this->payments()->count();
        if ($totalPayments === 0) {
            return 0;
        }

        $successfulPayments = $this->getSuccessfulPaymentsCount();
        return ($successfulPayments / $totalPayments) * 100;
    }

    /**
     * Check if the payment method supports webhooks.
     */
    public function supportsWebhooks(): bool
    {
        return $this->isOnline() && in_array($this->code, [
            'stripe',
            'yookassa',
            'paypal',
            'robokassa',
        ]);
    }

    /**
     * Get recent payments for this method.
     */
    public function recentPayments(int $limit = 10): HasMany
    {
        return $this->payments()->latest()->limit($limit);
    }

    /**
     * Get pending payments for this method.
     */
    public function pendingPayments(): HasMany
    {
        return $this->payments()->where('status', 'pending');
    }

    /**
     * Get failed payments for this method.
     */
    public function failedPayments(): HasMany
    {
        return $this->payments()->where('status', 'failed');
    }

    /**
     * Set short_code attribute.
     * If empty then make it new based on code attribute
     */
    public function setShortCodeAttribute($value): void
    {
        if (empty($value)) { // If empty then make it new based on code attribute
            $value = strtolower(preg_replace('/[^A-Za-z0-9]/', '', $this->code));
        }

        $this->attributes['short_code'] = $value;
    }

}
