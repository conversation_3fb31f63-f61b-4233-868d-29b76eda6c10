<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Payment extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'public_id',
        'order_id',
        'method_id',
        'status',
        'amount',
        'currency',
        'external_payment_id',
        'external_details',
        'raw_payload',
        'paid_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'amount' => 'integer',
            'raw_payload' => 'array',
            'external_details' => 'array',
            'paid_at' => 'datetime',
        ];
    }


    /* ---------------- Relationships ---------------- */

    /**
     * Get the order this payment belongs to.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the payment method used.
     */
    public function method(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class, 'method_id');
    }

    /**
     * Get the webhooks for this payment.
     */
    public function webhooks(): HasMany
    {
        return $this->hasMany(PaymentWebhook::class);
    }

    /**
     * Get the user who made this payment.
     */
    public function user(): BelongsTo
    {
        return $this->order->user();
    }


    /* ---------------- Scopes ---------------- */

    /**
     * Scope to get payments with specific status.
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending payments.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get paid payments.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope to get failed payments.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope to get cancelled payments.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }


    /* ---------------- Events ---------------- */

    protected static function booted(): void
    {
        static::creating(function (self $payment) {
            if (empty($payment->public_id)) {
                // Надёжно получаем метод
                $method = $payment->relationLoaded('method')
                    ? $payment->method
                    : PaymentMethod::find($payment->method_id);

                if (!$method) {
                    throw new \RuntimeException('Cannot generate public_id: Payment method not found.');
                }

                $payment->public_id = static::generatePublicId($method);
            }
        });
    }


    /* ---------------- State Checks ---------------- */

    /**
     * Check if the payment is successful.
     */
    public function isSuccessful(): bool
    {
        return $this->status === 'paid' && $this->paid_at !== null;
    }

    /**
     * Check if the payment is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the payment has failed.
     */
    public function hasFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if the payment is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if the payment is recent (within last hour).
     */
    public function isRecent(): bool
    {
        return now()->diffInHours($this->created_at) <= 1;
    }


    /* ---------------- Accessors & Mutators ---------------- */

    /**
     * Get the formatted amount in major units.
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount / 100, 2) . ' ' . $this->currency;
    }

    /**
     * Get the time elapsed since payment creation.
     */
    public function getTimeElapsedAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }


    /* ---------------- Actions ---------------- */

    /**
     * Mark the payment as paid.
     */
    public function markAsPaid(): void
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now(),
        ]);

        // Also mark the order as paid
        if ($this->order && !$this->order->isPaid()) {
            $this->order->markAsPaid();
        }
    }

    /**
     * Mark the payment as failed.
     */
    public function markAsFailed(): void
    {
        $this->update(['status' => 'failed']);

        // Mark the order as failed if this was the only payment attempt
        if ($this->order && $this->order->payments()->count() === 1) {
            $this->order->markAsFailed();
        }
    }

    /**
     * Mark the payment as cancelled.
     */
    public function markAsCancelled(): void
    {
        $this->update(['status' => 'cancelled']);
    }


    /* ---------------- Helpers / Utilities ---------------- */

    /**
     * Generate a unique public ID for the Payment.
     */
    public static function generatePublicId(PaymentMethod $method): string
    {
        $prefix = 'PAY-' . Str::upper($method->short_code) . '-';

        do {
            $randomString = Str::upper(Str::random(7));
            $publicId = $prefix . $randomString;
        } while (static::where('public_id', $publicId)->exists());

        return $publicId;
    }

    /**
     * Find a payment by its public ID.
     */
    public static function findByPublicId(string $publicId): ?Payment
    {
        return static::where('public_id', $publicId)->first();
    }
}
