<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

class Subscription extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'plan_id',
        'start_date',
        'end_date',
        'status',
        'is_manual_extension',
        'notes',
        'admin_notes',
        'traffic_used_bytes',
        'traffic_up_bytes',
        'traffic_down_bytes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'start_date' => 'datetime',
            'end_date' => 'datetime',
            'is_manual_extension' => 'boolean',
            'traffic_used_bytes' => 'integer',
            'traffic_up_bytes' => 'integer',
            'traffic_down_bytes' => 'integer',
        ];
    }


    /* ---------------- Relationships ---------------- */

    /**
     * Get the user that owns the subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription plan. (alias for subscriptionPlan())
     */
    public function plan(): BelongsTo
    {
        return $this->subscriptionPlan();
    }

    /**
     * Get the subscription plan.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class, 'plan_id');
    }

    /**
     * Get the subscription history records.
     */
    public function history(): HasMany
    {
        return $this->hasMany(SubscriptionHistory::class);
    }

    /**
     * Get the traffic logs for this subscription.
     */
    public function trafficLogs(): HasMany
    {
        return $this->hasMany(UserTrafficLog::class);
    }

    /**
     * Get the access logs for this subscription.
     */
    public function accessLogs(): HasMany
    {
        return $this->hasMany(SubscriptionAccessLog::class);
    }


    /* ---------------- Scopes ---------------- */

    /**
     * Scope to get only active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get only expired subscriptions.
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    /**
     * Scope to get only cancelled subscriptions.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }


    /* ---------------- Accessors & Mutators ---------------- */

    /**
     * Set the autogenerated notes if notes is empty
     */
    public function setNotesAttribute($value)
    {
        if (empty($value)) {
            $value = $this->generateNotes();
        }
        $this->attributes['notes'] = $value;
    }


    /* ---------------- State Checks ---------------- */

    /**
     * Check if the subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' &&
               (!$this->end_date || $this->end_date->isFuture());
    }

    /**
     * Check if the subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired' ||
               ($this->end_date && $this->end_date->isPast());
    }

    /**
     * Check if the subscription is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if the subscription is demo.
     */
    public function isDemo(): bool
    {
        return $this->plan && $this->plan->isDemo();
    }

    /**
     * Check if the subscription is about to expire.
     */
    public function isAboutToExpire(int $days = 3): bool
    {
        if (! $this->end_date || ! $this->isActive() || $this->isExpired()) {
            return false;
        }

        return now()->diffInDays($this->end_date) <= $days;
    }

    /**
     * Check if renewal is required (expired or about to expire).
     */
    public function isRenewalRequired(): bool
    {
        return ! $this->isActive() || $this->isExpired() || $this->isAboutToExpire();
    }

    /**
     * Check if traffic limit is exceeded.
     */
    public function isTrafficLimitExceeded(): bool
    {
        if (!$this->plan || !$this->plan->traffic_limit_bytes) {
            return false;
        }
        return $this->traffic_used_bytes >= $this->plan->traffic_limit_bytes;
    }


    /* ---------------- Actions ---------------- */

    /**
     * Cancel the subscription.
     */
    public function cancel(): void
    {
        $this->update([
            'status' => 'cancelled',
        ]);
    }

    /**
     * Expire the subscription.
     */
    public function expire(): void
    {
        $this->update([
            'status' => 'expired',
        ]);
    }

    /**
     * Renew the subscription to a new end date.
     */
    public function renewTo(?Carbon $newEndDate): void
    {
        $this->update([
            'end_date' => $newEndDate,
            'status' => 'active',
            'notes' => $this->generateNotes(),
        ]);
    }

    /**
     * Extend the subscription to a new end date.
     */
    public function extendTo(?Carbon $newEndDate): void
    {
        $this->update([
            'end_date' => $newEndDate,
            'status' => 'active',
            'notes' => $this->generateNotes(),
        ]);
    }

    /**
     * Change the end date of the subscription.
     */
    public function changeEndDate(?Carbon $newEndDate): void
    {
        $this->update([
            'end_date' => $newEndDate,
            'notes' => $this->generateNotes(),
        ]);
    }

    /**
     * Upgrade the subscription to a new plan.
     */
    public function changePlan(SubscriptionPlan $newPlan, ?Carbon $newEndDate): void
    {
        $this->update([
            'plan_id' => $newPlan->id,
            'end_date' => $newEndDate,
            'notes' => $this->generateNotes(),
        ]);
    }

    /**
     * Update the traffic usage for the subscription.
     */
    public function updateTrafficUsage(int $downBytes, int $upBytes)
    {
        $totalBytes = $downBytes + $upBytes;

        $this->traffic_used_bytes = $totalBytes;
        $this->traffic_up_bytes = $upBytes;
        $this->traffic_down_bytes = $downBytes;
        return $this->save();

        // $this->update([
        //     'traffic_used_bytes' => $totalBytes,
        //     'traffic_up_bytes' => $upBytes,
        //     'traffic_down_bytes' => $downBytes,
        // ]);
    }


    /* ---------------- Helpers / Utilities ---------------- */

    /**
     * Get the expiry time in milliseconds. For XUI server client.
     */
    public function getExpiryTimeMs(): int
    {
        return $this->end_date ? $this->end_date->getTimestampMs() : 0;
    }

    /**
     * Get the remaining days until expiration.
     */
    public function getRemainingDays(): ?int
    {
        if (!$this->end_date) {
            return null;
        }
        return max(0, now()->diffInDays($this->end_date));
    }

    /**
     * Get the traffic usage percentage.
     */
    public function getTrafficUsagePercentage(): ?float
    {
        if (!$this->plan || !$this->plan->traffic_limit_bytes) {
            return null;
        }
        return min(100, ($this->traffic_used_bytes / $this->plan->traffic_limit_bytes) * 100);
    }

    /**
     * Get the remaining traffic in bytes.
     */
    public function getRemainingTrafficBytes(): ?int
    {
        if (!$this->plan || !$this->plan->traffic_limit_bytes) {
            return null;
        }
        return max(0, $this->plan->traffic_limit_bytes - $this->traffic_used_bytes);
    }

    /**
     * Get the formatted price based on plan's price and currency
     */
    public function toFormattedPrice(): string
    {
        return number_format($this->plan->price / 100, 2) . ' ' . $this->plan->currency;
    }

    /**
     * Generate notes for the subscription.
     */
    public function generateNotes(?SubscriptionPlan $plan = null): string
    {
        $plan = $plan ?? $this->plan;

        // notes from plan name, duration, and price
        return 'Subscription for '
            . $plan->name . ' for '
            . $plan->duration . ' ' . $plan->duration_units
            . ' at a cost of ' . $plan->toFormattedPrice();
    }

}
