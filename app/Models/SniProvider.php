<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SniProvider extends Model
{
    protected $fillable = [
        'category_id',
        'name',
        'label',
        'enabled',
        'notes',
    ];

    protected $casts = [
        'enabled' => 'boolean',
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(SniCategory::class, 'category_id');
    }

    public function endpoints(): HasMany
    {
        return $this->hasMany(SniEndpoint::class, 'provider_id');
    }

    public function enabledEndpoints(): HasMany
    {
        return $this->endpoints()->where('enabled', true);
    }

    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }
}