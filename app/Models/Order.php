<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class Order extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'public_id',
        'user_id',
        'status', // enum ['new', 'pending', 'processing', 'paid', 'completed', 'failed', 'cancelled']
        'total_amount',
        'currency',
        'paid_at',
        'expires_at',
        'notes',
        'admin_notes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'total_amount' => 'integer',
            'paid_at' => 'datetime',
            'expires_at' => 'datetime',
        ];
    }

    /**
     * Get the user that owns the order.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the order items.
     */
    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the payments for this order.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Scope to get orders with specific status.
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get new orders.
     */
    public function scopeNew($query)
    {
        return $query->where('status', 'new');
    }

    /**
     * Scope to get pending orders.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get processing orders.
     */
    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }

    /**
     * Scope to get paid orders.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope to get failed orders.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope to get cancelled orders.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Scope to get expired orders.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    /**
     * Check if the order is paid.
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid' && $this->paid_at !== null;
    }

    /**
     * Check if the order is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if the order can be paid.
     */
    public function canBePaid(): bool
    {
        return in_array($this->status, ['new', 'pending', 'processing']) && !$this->isExpired();
    }

    /**
     * Get the formatted total amount in major units.
     */
    public function getFormattedTotalAmountAttribute(): string
    {
        return number_format($this->total_amount / 100, 2) . ' ' . $this->currency;
    }

    /**
     * Get the successful payment for this order.
     */
    public function successfulPayment(): ?Payment
    {
        return $this->payments()->where('status', 'paid')->first();
    }

    /**
     * Calculate the total amount from order items.
     */
    public function calculateTotalAmount(): int
    {
        return $this->items()->sum(DB::raw('quantity * unit_price'));
    }

    /**
     * Update the total amount based on order items.
     */
    public function updateTotalAmount(): void
    {
        $this->update(['total_amount' => $this->calculateTotalAmount()]);
    }

    /**
     * Mark the order as paid.
     */
    public function markAsPaid(): void
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now(),
        ]);
    }

    /**
     * Mark the order as failed.
     */
    public function markAsFailed(): void
    {
        $this->update(['status' => 'failed']);
    }

    /**
     * Mark the order as cancelled.
     */
    public function markAsCancelled(): void
    {
        $this->update(['status' => 'cancelled']);
    }

    /**
     * Get the remaining time until expiration.
     */
    public function getRemainingTime(): ?string
    {
        if (!$this->expires_at || $this->isExpired()) {
            return null;
        }

        return $this->expires_at->diffForHumans();
    }

    /**
     * Boot the model.
     */
    protected static function booted(): void
    {
        // generate public ID on creation
        static::creating(function (self $order) {
            if (empty($order->public_id)) {
                $order->public_id = static::generatePublicId();
            }
        });
    }

    /**
     * Generate a unique public ID for the Order.
     */
    public static function generatePublicId(): string
    {
        $prefix = 'ORD-';

        do {
            $randomString = Str::upper(Str::random(7));
            $publicId = $prefix . $randomString;
            // check if the public ID already exists
        } while (static::where('public_id', $publicId)->exists());

        return $publicId;
    }

    /**
     * Find an order by its public ID.
     */
    public static function findByPublicId(string $publicId): ?Order
    {
        return static::where('public_id', $publicId)->first();
    }
}
