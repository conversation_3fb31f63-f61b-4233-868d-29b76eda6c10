<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    use HasFactory;

    protected static $cacheTtl = 1; // seconds

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'key',
        'value',
        'type',
        'notes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'value' => 'string', // We'll handle casting manually based on type
        ];
    }

    /**
     * Scope to get setting by key.
     */
    public function scopeByKey($query, string $key)
    {
        return $query->where('key', $key);
    }

    /**
     * Scope to get settings by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get the typed value of the setting.
     */
    public function getTypedValueAttribute()
    {
        return $this->castValue($this->value, $this->type);
    }

    /**
     * Set the value with automatic type detection.
     */
    public function setTypedValue($value): void
    {
        $this->type = $this->detectType($value);
        $this->value = $this->prepareValueForStorage($value);
    }

    /**
     * Cast value based on type.
     */
    private function castValue($value, $type)
    {
        if ($value === null) {
            return null;
        }

        return match ($type) {
            'int' => (int) $value,
            'float' => (float) $value,
            'bool' => filter_var($value, FILTER_VALIDATE_BOOLEAN),
            'array', 'json' => json_decode($value, true),
            default => $value,
        };
    }

    /**
     * Detect the type of a value.
     */
    private function detectType($value): string
    {
        if (is_int($value)) {
            return 'int';
        }

        if (is_float($value)) {
            return 'float';
        }

        if (is_bool($value)) {
            return 'bool';
        }

        if (is_array($value)) {
            return 'array';
        }

        return 'string';
    }

    /**
     * Prepare value for storage.
     */
    private function prepareValueForStorage($value): string
    {
        if (is_array($value)) {
            return json_encode($value);
        }

        if (is_bool($value)) {
            return $value ? '1' : '0';
        }

        return (string) $value;
    }

    /**
     * Get a setting value by key.
     */
    public static function get(string $key, $default = null)
    {
        $cacheKey = "setting.{$key}";

        return Cache::remember($cacheKey,self::$cacheTtl, function () use ($key, $default) {
            $setting = static::byKey($key)->first();

            if (!$setting) {
                return $default;
            }

            return $setting->typed_value;
        });
    }

    /**
     * Set a setting value by key.
     */
    public static function set(string $key, $value, ?string $notes = null): self
    {
        $setting = static::byKey($key)->first();

        if ($setting) {
            $setting->setTypedValue($value);
            if ($notes !== null) {
                $setting->notes = $notes;
            }
            $setting->save();
        } else {
            $setting = new static([
                'key' => $key,
                'notes' => $notes,
            ]);
            $setting->setTypedValue($value);
            $setting->save();
        }

        // Clear cache
        Cache::forget("setting.{$key}");

        return $setting;
    }

    /**
     * Check if a setting exists.
     */
    public static function has(string $key): bool
    {
        return static::byKey($key)->exists();
    }

    /**
     * Delete a setting by key.
     */
    public static function forget(string $key): bool
    {
        $deleted = static::byKey($key)->delete();
        Cache::forget("setting.{$key}");
        return $deleted > 0;
    }

    /**
     * Delete multiple settings by keys.
     */
    public static function forgetMultiple(array $keys): array
    {
        $deleted = [];
        foreach ($keys as $key) {
            $deleted[] = self::forget($key);
        }
        return $deleted;
    }

    /**
     * Get all settings as an associative array.
     */
    public static function getAllSettings(): array
    {
        return Cache::remember('settings.all', self::$cacheTtl, function () {
            return static::query()->get()->pluck('typed_value', 'key')->toArray();
        });
    }

    /**
     * Get settings by type.
     */
    public static function getByType(string $type): array
    {
        return static::byType($type)->get()->pluck('typed_value', 'key')->toArray();
    }

    /**
     * Bulk set multiple settings.
     */
    public static function setMultiple(array $settings): void
    {
        foreach ($settings as $key => $value) {
            static::set($key, $value);
        }
    }

    /**
     * Get common routing rules setting.
     */
    public static function getCommonRoutingRules(): array
    {
        $rules = static::get('common_routing_rules', '');

        if (empty($rules)) {
            return [];
        }

        // If it's base64 encoded, decode it
        if (is_string($rules) && base64_encode(base64_decode($rules, true)) === $rules) {
            $rules = base64_decode($rules);
        }

        return is_array($rules) ? $rules : (json_decode($rules, true) ?: []);
    }

    /**
     * Set common routing rules setting.
     */
    public static function setCommonRoutingRules(array $rules): void
    {
        static::set('common_routing_rules', $rules, 'Common routing rules for VPN clients (base64 encoded).');
    }

    /**
     * Get renewal threshold days setting.
     */
    public static function getRenewalThresholdDays(): int
    {
        return static::get('renewal_threshold_days', 3);
    }

    /**
     * Set renewal threshold days setting.
     */
    public static function setRenewalThresholdDays(int $days): void
    {
        static::set('renewal_threshold_days', $days, 'Number of days before the subscription expires. For example, before which the renewal button will be displayed');
    }

    /**
     * Clear all settings cache.
     */
    public static function clearCache(): void
    {
        Cache::forget('settings.all');

        // Clear individual setting caches
        $keys = static::pluck('key');
        foreach ($keys as $key) {
            Cache::forget("setting.{$key}");
        }
    }

    /**
     * Boot the model.
     */
    protected static function booted(): void
    {
        static::saved(function (Setting $setting) {
            Cache::forget("setting.{$setting->key}");
            Cache::forget('settings.all');
        });

        static::deleted(function (Setting $setting) {
            Cache::forget("setting.{$setting->key}");
            Cache::forget('settings.all');
        });
    }
}
