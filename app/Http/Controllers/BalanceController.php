<?php

namespace App\Http\Controllers;

use App\DTOs\Balance\BalanceTopUpDTO;
use App\DTOs\Balance\CombinedPaymentDTO;
use App\Enums\BalanceTransactionSource;
use App\Models\Order;
use App\Models\User;
use App\Services\BalanceService;
use App\Services\OrderService;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BalanceController extends Controller
{
    public function __construct(
        private BalanceService $balanceService,
        private OrderService $orderService,
        private PaymentService $paymentService
    ) {}

    /**
     * Show user balance dashboard.
     */
    public function index(string $uuid)
    {
        $user = User::findByUuid($uuid);
        $balance = $user->getTotalBalanceAmount();
        $recentTransactions = $user->getRecentBalanceTransactions(10);

        return view('balance.index', compact('user', 'balance', 'recentTransactions'));
    }

    /**
     * Show balance top-up form.
     */
    public function topUpForm()
    {
        return view('balance.topup');
    }

    /**
     * Create balance top-up order.
     */
    public function createTopUp(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:1|max:100000',
            'payment_method' => 'required|string|in:tbank,manual,cash',
        ]);

        try {
            $user = Auth::user();
            $amount = (float) $request->amount;
            $paymentMethod = $request->payment_method;

            // Create balance top-up order
            $order = $this->orderService->createBalanceTopUpOrder(
                user: $user,
                amount: $amount,
                currency: 'RUB',
                description: "Balance top-up: {$amount} RUB"
            );

            // Create payment for the order
            $paymentResult = $this->paymentService->createPayment(
                order: $order,
                methodCode: $paymentMethod
            );

            if ($paymentResult->isSuccess()) {
                return redirect()->route('balance.order.status', $order->public_id)
                    ->with('success', 'Top-up order created successfully');
            } else {
                return back()->withErrors(['payment' => $paymentResult->message]);
            }

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create top-up order: ' . $e->getMessage()]);
        }
    }

    /**
     * Show order status.
     */
    public function orderStatus(string $orderPublicId)
    {
        $order = Order::findByPublicId($orderPublicId);

        if (!$order) {
            abort(404);
        }

        return view('balance.order-status', compact('order'));
    }

    /**
     * Pay order using balance only.
     */
    public function payWithBalance(Request $request, string $orderPublicId)
    {
        $order = Order::findByPublicId($orderPublicId);

        if (!$order) {
            abort(404);
        }

        if ($order->isPaid()) {
            return back()->with('info', 'Order is already paid');
        }

        try {
            /** @var User $user */
            $user = $order->user;
            $orderAmount = $order->total_amount;

            // Check if user can afford the order
            if (!$user->canAfford($orderAmount)) {
                return back()->withErrors(['balance' => 'Insufficient balance']);
            }

            // Process balance-only payment
            $success = $this->orderService->processBalanceOnlyPayment($order);

            if ($success) {
                return back()->with('success', 'Order paid successfully using balance');
            } else {
                return back()->withErrors(['payment' => 'Failed to process balance payment']);
            }

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Payment failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Pay order using combined payment (balance + external).
     */
    public function payWithCombined(Request $request, string $orderPublicId)
    {
        $request->validate([
            'balance_amount' => 'required|numeric|min:0',
            'external_method' => 'required|string|in:tbank,manual,cash',
        ]);

        $order = Order::findByPublicId($orderPublicId);

        if (!$order || $order->user_id !== Auth::id()) {
            abort(404);
        }

        if ($order->isPaid()) {
            return back()->with('info', 'Order is already paid');
        }

        try {
            $user = Auth::user();
            $balanceAmount = (float) $request->balance_amount;
            $externalMethod = $request->external_method;
            $orderAmount = $order->total_amount / 100;

            if ($balanceAmount > $orderAmount) {
                return back()->withErrors(['balance_amount' => 'Balance amount cannot exceed order amount']);
            }

            if ($balanceAmount > 0 && !$user->canAfford($balanceAmount)) {
                return back()->withErrors(['balance_amount' => 'Insufficient balance']);
            }

            // Create combined payment
            $paymentResult = $this->paymentService->createCombinedPayment(
                order: $order,
                balanceAmount: $balanceAmount,
                externalMethod: $externalMethod
            );

            if ($paymentResult->isSuccess()) {
                if ($paymentResult->paymentUrl) {
                    return redirect($paymentResult->paymentUrl);
                } else {
                    return back()->with('success', 'Combined payment processed successfully');
                }
            } else {
                return back()->withErrors(['payment' => $paymentResult->message]);
            }

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Combined payment failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Get balance information for AJAX requests.
     */
    public function getBalanceInfo()
    {
        $user = Auth::user();

        return response()->json([
            'balance' => $user->getBalance(),
            'balance_formatted' => $user->getFormattedBalance(),
            'can_afford' => function($amount) use ($user) {
                return $user->canAfford($amount);
            }
        ]);
    }

    /**
     * Get balance transaction history.
     */
    public function transactionHistory()
    {
        $user = Auth::user();
        $transactions = $user->getBalanceTransactionHistory(20);

        return view('balance.transactions', compact('transactions'));
    }

    /**
     * Manual balance adjustment (admin only).
     */
    public function manualAdjustment(Request $request, User $user)
    {
        // This should be protected by admin middleware
        $request->validate([
            'amount' => 'required|numeric|not_in:0',
            'description' => 'required|string|max:255',
        ]);

        try {
            $amount = (float) $request->amount;
            $description = $request->description;

            if ($amount > 0) {
                $transaction = $this->balanceService->addFunds(
                    user: $user,
                    amount: $amount,
                    source: BalanceTransactionSource::MANUAL_ADJUSTMENT,
                    description: $description
                );
            } else {
                $transaction = $this->balanceService->deductFunds(
                    user: $user,
                    amount: abs($amount),
                    source: BalanceTransactionSource::MANUAL_ADJUSTMENT,
                    description: $description
                );
            }

            return back()->with('success', 'Balance adjusted successfully');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Balance adjustment failed: ' . $e->getMessage()]);
        }
    }
}
