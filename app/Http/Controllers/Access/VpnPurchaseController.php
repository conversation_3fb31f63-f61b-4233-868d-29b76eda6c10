<?php

namespace App\Http\Controllers\Access;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\PaymentMethod;
use App\Services\OrderService;
use App\Services\PaymentService;
use App\Services\SubscriptionAnalyzerService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class VpnPurchaseController extends Controller
{
    public function __construct(
        private OrderService $orderService,
        private PaymentService $paymentService,
        private SubscriptionAnalyzerService $subscriptionAnalyzer,
    ) {}

    /**
     * Process plan selection and payment method choice, create an order, and generate payment.
     */
    public function purchase(Request $request, string $uuid): RedirectResponse | JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'plan_id' => 'required|exists:subscription_plans,id',
                'payment_method' => 'required|exists:payment_methods,code',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse('Validation failed', $validator->errors()->toArray());
            }

            // Find user by UUID (id field is UUID in this model)
            /** @var \App\Models\User $user */
            $user = User::where('id', $uuid)->firstOrFail();

            // Get subscription plan
            $plan = SubscriptionPlan::findOrFail($request->plan_id);

            // 1. Определяем, что делает пользователь (новая подписка, продление, апгрейд, даунгрейд)
            $actionType = $this->subscriptionAnalyzer->detectSubscriptionAction($user, $plan);

            // Get payment method
            $paymentMethod = PaymentMethod::where('code', $request->payment_method)
                ->where('is_active', true)
                ->firstOrFail();

            $useBalance = $request->boolean('use_balance');
            $planPrice = $plan->price;
            $userBalance = $user->getTotalBalanceAmount();

            // Создаем заказ
            $order = $this->orderService->createVpnSubscriptionOrder($user, $plan, $actionType);

            if ($useBalance) {

                if ($userBalance >= $planPrice) {
                    // Полная оплата с баланса
                    $paymentResult = $this->paymentService->createBalancePayment($order);
                } else {
                    // Частичная оплата: баланс + способ оплаты
                    $amountFromBalance = $userBalance;
                    $amountFromOther = $planPrice - $userBalance;

                    $paymentResult = $this->paymentService->createCombinedPayment(
                        $order,
                        $amountFromBalance,
                        $paymentMethod->code
                    );
                }
            } else {
                // Обычная оплата полностью выбранным способом
                $paymentResult = $this->paymentService->createPayment($order, $paymentMethod->code);
            }

            if ($paymentResult->isSuccess()) {
                Log::info('VPN purchase initiated successfully', [
                    'user_uuid' => $user->id,
                    'orderPublicId' => $order->public_id,
                    'plan_id' => $plan->id,
                    'payment_method' => $paymentMethod->code,
                    'amount' => $order->total_amount,
                ]);

                if ($paymentResult->paymentUrl) {
                    return redirect($paymentResult->paymentUrl);
                }

                return redirect()->route('access.payment.success', [
                    'uuid' => $user->id,
                    'orderPublicId' => $order->public_id
                ]);
            }

            return redirect()->route('access.payment.failed', [
                'uuid' => $user->id,
                'orderPublicId' => $order->public_id
            ])->with('error', $paymentResult->message);

        } catch (\Exception $e) {
            Log::error('VPN purchase exception', [
                'user_uuid' => $uuid,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return $this->errorResponse('Purchase failed: ' . $e->getMessage());
        }
    }

    /**
     * Return error response.
     */
    private function errorResponse(string $message, array $errors = []): JsonResponse|RedirectResponse
    {
        if (request()->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => $message,
                'errors' => $errors,
            ], 400);
        }

        return back()->withErrors(['error' => $message])->withInput();
    }
}
