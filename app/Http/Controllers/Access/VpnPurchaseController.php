<?php

namespace App\Http\Controllers\Access;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\PaymentMethod;
use App\Services\OrderService;
use App\Services\PaymentService;
use App\Services\SubscriptionAnalyzerService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class VpnPurchaseController extends Controller
{
    public function __construct(
        private OrderService $orderService,
        private PaymentService $paymentService,
        private SubscriptionAnalyzerService $subscriptionAnalyzer,
    ) {}

    /**
     * Process plan selection and payment method choice, create an order, and generate payment.
     */
    public function purchase(Request $request, string $uuid): RedirectResponse | JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'plan_id' => 'required|exists:subscription_plans,id',
                'payment_method' => 'required|exists:payment_methods,code',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse('Validation failed', $validator->errors()->toArray());
            }

            // Find user by UUID (id field is UUID in this model)
            $user = User::where('id', $uuid)->firstOrFail();

            // Get subscription plan
            $plan = SubscriptionPlan::findOrFail($request->plan_id);

            // 1. Определяем, что делает пользователь (новая подписка, продление, апгрейд, даунгрейд)
            $actionType = $this->subscriptionAnalyzer->detectSubscriptionAction($user, $plan);

            // Get payment method
            $paymentMethod = PaymentMethod::where('code', $request->payment_method)
                ->where('is_active', true)
                ->firstOrFail();

            // 2. Создаем заказ
            if ($paymentMethod->code === 'balance') {
                // Check if user can afford the order
                if (!$user->canAfford($plan->price)) {
                    throw new \Exception('Insufficient balance');
                }
                $order = $this->orderService->createVpnSubscriptionOrder($user, $plan, $actionType);
                $paymentResult = $this->paymentService->createBalancePayment($order);
                if ($paymentResult->isSuccess()) {
                    return redirect()->route('access.payment.success', [
                        'uuid' => $user->id,
                        'order' => $order->public_id
                    ]);
                } else {
                    return redirect()->route('access.payment.failed', [
                        'uuid' => $user->id,
                        'order' => $order->public_id
                    ])->with('error', $paymentResult->message);
                }
            }

            if ($paymentMethod->code === 'combined') {
                // Check if user can afford the order
                if (!$user->canAfford($plan->price)) {
                    throw new \Exception('Insufficient balance');
                }
                $order = $this->orderService->createVpnSubscriptionOrder($user, $plan, $actionType);
                $paymentResult = $this->paymentService->createCombinedPayment($order, 0, 'tbank');
                if ($paymentResult->isSuccess()) {
                    return redirect()->route('access.payment.success', [
                        'uuid' => $user->id,
                        'order' => $order->public_id
                    ]);
                } else {
                    return redirect()->route('access.payment.failed', [
                        'uuid' => $user->id,
                        'order' => $order->public_id
                    ])->with('error', $paymentResult->message);
                }
            }

            // Create order
            $order = $this->orderService->createVpnSubscriptionOrder($user, $plan, $actionType);

            // Create payment
            $paymentResult = $this->paymentService->createPayment($order, $paymentMethod->code);

            if ($paymentResult->isSuccess()) {
                Log::info('VPN purchase initiated successfully', [
                    'user_uuid' => $user->id,
                    'order_id' => $order->public_id,
                    'plan_id' => $plan->id,
                    'payment_method' => $paymentMethod->code,
                    'amount' => $order->total_amount,
                ]);

                // If payment has a URL (like T-Bank), redirect to it
                if ($paymentResult->paymentUrl) {
                    return redirect($paymentResult->paymentUrl);
                }

                // For instant payments (free, manual), redirect to success
                if ($paymentResult->status === 'paid') {
                    return redirect()->route('access.payment.success', [
                        'uuid' => $user->id,
                        'order' => $order->public_id
                    ]);
                }

                // For pending payments, show waiting page or redirect to success
                return redirect()->route('access.payment.success', [
                    'uuid' => $user->id,
                    'order' => $order->public_id
                ]);

            } else {
                Log::error('VPN purchase failed', [
                    'user_uuid' => $user->id,
                    'order_id' => $order->public_id,
                    'error' => $paymentResult->message,
                ]);

                return redirect()->route('access.payment.failed', [
                    'uuid' => $user->id,
                    'order' => $order->public_id
                ])->with('error', $paymentResult->message);
            }

        } catch (\Exception $e) {
            Log::error('VPN purchase exception', [
                'user_uuid' => $user->id,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return $this->errorResponse('Purchase failed: ' . $e->getMessage());
        }
    }

    /**
     * Return error response.
     */
    private function errorResponse(string $message, array $errors = []): JsonResponse|RedirectResponse
    {
        if (request()->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => $message,
                'errors' => $errors,
            ], 400);
        }

        return back()->withErrors(['error' => $message])->withInput();
    }
}
