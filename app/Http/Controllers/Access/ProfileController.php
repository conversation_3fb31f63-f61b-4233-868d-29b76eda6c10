<?php

namespace App\Http\Controllers\Access;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\Access\AccessService;
use App\Services\HelperService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;

class ProfileController extends Controller
{
    public function __construct(
        private AccessService $accessService,
        private HelperService $helperService
    ) {}

    /**
     * Handle both GET and POST requests for user profile/subscription access.
     * GET: Returns HTML profile page for browsers
     * POST: Returns VLESS keys with headers for VPN clients
     */
    public function show(Request $request, string $uuid): Response|View
    {
        $startTime = microtime(true);

        try {
            // Validate UUID format early
            if (!$this->helperService->isValidUuid($uuid)) {
                Log::warning("Invalid UUID format: {$uuid}", [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);
                return response('Invalid UUID format', 400);
            }

            // Find user by UUID
            $user = User::where('id', $uuid)->first();
            if (!$user) {
                Log::info("User not found for UUID: {$uuid}", [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);
                return response('User not found', 404);
            }

            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            // Check if request is from a browser (GET) or VPN client (POST)
            if ($this->helperService->isBrowserUserAgent($request)) {
                // Return HTML view for browser requests
                Log::info("Profile page delivered for UUID: {$uuid}", [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'user_id' => $user->id,
                    'response_time_ms' => $responseTime,
                ]);

                return $this->renderProfileView($user, $uuid);
            } else {
                // Return VLESS content with headers for VPN clients
                $vlessContent = $this->accessService->generateVlessKeys($user);
                $headers = $this->accessService->getSubscriptionHeaders($user, $uuid);

                Log::info("VLESS keys delivered for UUID: {$uuid}", [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'user_id' => $user->id,
                    'content_length' => strlen($vlessContent),
                    'response_time_ms' => $responseTime,
                ]);

                return response($vlessContent, 200, $headers);
            }

        } catch (\Exception $e) {
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::error("Error delivering content for UUID: {$uuid}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'response_time_ms' => $responseTime,
            ]);

            return response('Internal server error', 500);
        }
    }

    public function updateSniCategory(Request $request, string $uuid): JsonResponse
    {
        try {
            if (!$this->helperService->isValidUuid($uuid)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid UUID format',
                ], 400);
            }

            $user = User::where('id', $uuid)->first();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found',
                ], 404);
            }

            $user->update([
                'sni_category' => ! $user->isSniMessenger() ? 'messengers' : null,
            ]);

            Log::info('User SNI category updated', [
                'user_id' => $user->id,
                'uuid' => $uuid,
                'tg_id' => $user->tg_id,
                'sni_category' => $request->sni_category,
                'ip' => $request->ip(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Настройка обновлена',
                'data' => [
                    'sni_category' => $user->sni_category,
                ],
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Некорректные данные',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Failed to update SNI category', [
                'uuid' => $uuid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Ошибка при обновлении настройки',
            ], 500);
        }
    }

    /**
     * Update user's common routing preference.
     */
    public function updateRoutingPreference(Request $request, string $uuid): JsonResponse
    {
        try {
            if (!$this->helperService->isValidUuid($uuid)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid UUID format',
                ], 400);
            }

            $user = User::where('id', $uuid)->first();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found',
                ], 404);
            }

            $request->validate([
                'use_common_routing' => 'required|boolean',
            ]);

            $user->update([
                'use_common_routing' => $request->use_common_routing,
            ]);

            Log::info('User routing preference updated', [
                'user_id' => $user->id,
                'uuid' => $uuid,
                'tg_id' => $user->tg_id,
                'use_common_routing' => $request->use_common_routing,
                'ip' => $request->ip(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Настройка обновлена',
                'data' => [
                    'use_common_routing' => $user->use_common_routing,
                ],
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Некорректные данные',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Failed to update routing preference', [
                'uuid' => $uuid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Ошибка при обновлении настройки',
            ], 500);
        }
    }

    /**
     * Render profile view for browser requests.
     */
    private function renderProfileView(User $user, string $uuid): View
    {
        $data = $this->accessService->prepareProfileData($user, $uuid);

        return view('access.profile', compact('data'));
    }
}
