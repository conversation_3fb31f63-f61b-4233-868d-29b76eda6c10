<?php

namespace App\DTOs\Xui;

/*
{
"network":"tcp",
"security":"reality",
"externalProxy":{
},
"realitySettings":{
"show":false,
"xver":0,
"dest":"yahoo.com:443",
"serverNames":[
"yahoo.com",
"www.yahoo.com"
],
"privateKey":"4E9QxbvVjhuPtjnt4XR3YGH0CSsXbLQvFipWSJHLpXs",
"minClient":"",
"maxClient":"",
"maxTimediff":0,
"shortIds":[
"138a",
"66",
"8a608f7e1af5da",
"66e985",
"dac465ec56",
"068ea0d7",
"03946d00d3c0",
"24592e1cba468e0b"
],
"settings":{
"publicKey":"UA70SUdtnWh6XuN_l9ZzH8wwfrW6BwfsMpUrn7AahTc",
"fingerprint":"chrome",
"serverName":"",
"spiderX":"/"
}
},
"tcpSettings":{
"acceptProxyProtocol":false,
"header":{
"type":"none"
}
}
}
*/

// to process an inbound settings like this
class InboundStreamSettingsDTO
{
    public function __construct(
        public string $network,
        public string $security,
        public array $externalProxy,
        public array $realitySettings,
        public array $tcpSettings
    ) {
    }

    public function toArray(): array
    {
        return [
            'network' => $this->network,
            'security' => $this->security,
            'externalProxy' => $this->externalProxy,
            'realitySettings' => $this->realitySettings,
            'tcpSettings' => $this->tcpSettings,
        ];
    }
}
