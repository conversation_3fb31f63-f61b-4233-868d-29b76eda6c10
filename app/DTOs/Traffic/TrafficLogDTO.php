<?php

namespace App\DTOs\Traffic;

use Illuminate\Support\Str;
use Illuminate\Support\Carbon;
use InvalidArgumentException;

class TrafficLogDTO
{
    public readonly string $user_id;
    public readonly ?string $subscription_id;
    public readonly int $xui_server_id;
    public readonly int $inbound_id;
    public readonly int $traffic_up_bytes;
    public readonly int $traffic_down_bytes;
    public readonly Carbon $data_as_of_timestamp;

    /**
     * @param array{
     *     user_id: int,
     *     subscription_id: string|null,
     *     xui_server_id: int,
     *     inbound_id: int,
     *     traffic_up_bytes: int,
     *     traffic_down_bytes: int,
     *     data_as_of_timestamp?: Carbon|string|null
     * } $data
     */
    public function __construct(array $data)
    {
        $this->user_id = $this->validateUuidField($data, 'user_id');
        $this->subscription_id = $this->validateNullableUuidField($data, 'subscription_id');
        $this->xui_server_id = $this->validateIntField($data, 'xui_server_id');
        $this->inbound_id = $this->validateIntField($data, 'inbound_id');
        $this->traffic_up_bytes = $this->validateIntField($data, 'traffic_up_bytes', true);
        $this->traffic_down_bytes = $this->validateIntField($data, 'traffic_down_bytes', true);

        $this->data_as_of_timestamp = isset($data['data_as_of_timestamp'])
            ? $this->parseTimestamp($data['data_as_of_timestamp'])
            : Carbon::now();
    }

    /**
     * Валидирует и возвращает UUID из массива данных
     *
     * @param array $data
     * @param string $field
     * @return string
     */
    protected function validateUuidField(array $data, string $field): string
    {
        if (!array_key_exists($field, $data)) {
            throw new InvalidArgumentException("Missing required field: {$field}");
        }

        if (!Str::isUuid($data[$field])) {
            throw new InvalidArgumentException("Field {$field} must be a string");
        }

        return $data[$field];
    }

    /**
     * Валидирует и возвращает UUID или null из массива данных
     *
     * @param array $data
     * @param string $field
     * @return string|null
     */
    protected function validateNullableUuidField(array $data, string $field): ?string
    {
        if (!array_key_exists($field, $data)) {
            return null;
        }

        if (is_null($data[$field])) {
            return null;
        }

        if (!Str::isUuid($data[$field])) {
            throw new InvalidArgumentException("Field {$field} must be a string");
        }

        return $data[$field];
    }

    /**
     * Валидирует и возвращает целое число из массива данных
     *
     * @param array $data
     * @param string $field
     * @param bool $allowZeroOrMore
     * @return int
     */
    protected function validateIntField(array $data, string $field, bool $allowZeroOrMore = false): int
    {
        if (!array_key_exists($field, $data)) {
            throw new InvalidArgumentException("Missing required field: {$field}");
        }

        if (!is_int($data[$field])) {
            throw new InvalidArgumentException("Field {$field} must be an integer");
        }

        if (!$allowZeroOrMore && $data[$field] <= 0) {
            throw new InvalidArgumentException("Field {$field} must be positive integer");
        }

        if ($allowZeroOrMore && $data[$field] < 0) {
            throw new InvalidArgumentException("Field {$field} must be zero or positive integer");
        }

        return $data[$field];
    }

    /**
     * Валидирует и возвращает целое число или null из массива данных
     *
     * @param array $data
     * @param string $field
     * @return int|null
     */
    protected function validateNullableIntField(array $data, string $field): ?int
    {
        if (!array_key_exists($field, $data)) {
            return null;
        }

        if (is_null($data[$field])) {
            return null;
        }

        if (!is_int($data[$field])) {
            throw new InvalidArgumentException("Field {$field} must be integer or null");
        }

        return $data[$field];
    }

    /**
     * Парсит timestamp в объект Carbon
     *
     * @param Carbon|string|null $value
     * @return Carbon
     */
    protected function parseTimestamp($value): Carbon
    {
        if ($value instanceof Carbon) {
            return $value;
        }

        if (is_string($value)) {
            return Carbon::parse($value);
        }

        throw new InvalidArgumentException("Invalid data_as_of_timestamp value");
    }

    /**
     * Возвращает массив для удобной сериализации
     *
     * @return array<string,mixed>
     */
    public function toArray(): array
    {
        return [
            'user_id' => $this->user_id,
            'subscription_id' => $this->subscription_id,
            'xui_server_id' => $this->xui_server_id,
            'inbound_id' => $this->inbound_id,
            'traffic_up_bytes' => $this->traffic_up_bytes,
            'traffic_down_bytes' => $this->traffic_down_bytes,
            'data_as_of_timestamp' => $this->data_as_of_timestamp->toDateTimeString(),
        ];
    }
}
