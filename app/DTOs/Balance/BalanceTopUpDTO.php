<?php

namespace App\DTOs\Balance;

use App\Models\User;
use App\Services\MoneyService;

class BalanceTopUpDTO
{
    /**
     * Data transfer object for balance top-up requests.
     *
     * @param User $user The user requesting the top-up
     * @param int $amount Amount to top up in minor currency units (e.g., kopecks, cents)
     * @param string $currency Currency code (e.g., 'RUB', 'USD')
     * @param string $paymentMethod Payment method code to use for the top-up
     * @param string|null $description Optional description for the top-up
     * @param array $additionalData Additional data for the payment gateway
     */
    public function __construct(
        public readonly User $user,
        public readonly int $amount,
        public readonly string $currency = 'RUB',
        public readonly string $paymentMethod = 'tbank',
        public readonly ?string $description = null,
        public readonly array $additionalData = []
    ) {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('Top-up amount must be positive');
        }

        if (empty($paymentMethod)) {
            throw new \InvalidArgumentException('Payment method is required');
        }
    }

    /**
     * Convert to array for order creation.
     */
    public function toArray(): array
    {
        return [
            'user_id' => $this->user->id,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'payment_method' => $this->paymentMethod,
            'description' => $this->description,
            'additional_data' => $this->additionalData,
        ];
    }

    /**
     * Get formatted amount for display.
     */
    public function getFormattedAmount(): string
    {
        return app(MoneyService::class)->getFormattedAmount($this->amount, $this->currency);
    }

    /**
     * Create a DTO from array data.
     */
    public static function fromArray(array $data): self
    {
        return new self(
            user: $data['user'],
            amount: $data['amount'],
            currency: $data['currency'] ?? 'RUB',
            paymentMethod: $data['payment_method'] ?? 'tbank',
            description: $data['description'] ?? null,
            additionalData: $data['additional_data'] ?? []
        );
    }

    /**
     * Get the order description for this top-up.
     */
    public function getOrderDescription(): string
    {
        return $this->description ?? "Balance top-up: " . $this->getFormattedAmount();
    }
}
