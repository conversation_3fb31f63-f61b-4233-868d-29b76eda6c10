<?php

namespace App\DTOs\Balance;

use App\Enums\BalanceTransactionSource;
use App\Enums\BalanceTransactionType;
use App\Models\Order;
use App\Models\Payment;
use App\Models\User;
use App\Services\MoneyService;

class BalanceTransactionDTO
{
    /**
     * Data transfer object for balance transaction data.
     *
     * @param User $user The user for the transaction
     * @param BalanceTransactionType $type Transaction type (credit/debit)
     * @param BalanceTransactionSource $source Transaction source
     * @param int $amount Amount in minor currency units
     * @param string $currency Currency code (e.g., 'RUB', 'USD')
     * @param string|null $description Optional description
     * @param Order|null $order Related order if applicable
     * @param Payment|null $payment Related payment if applicable
     */
    public function __construct(
        public readonly User $user,
        public readonly BalanceTransactionType $type,
        public readonly BalanceTransactionSource $source,
        public readonly int $amount,
        public readonly string $currency = 'RUB',
        public readonly ?string $description = null,
        public readonly ?Order $order = null,
        public readonly ?Payment $payment = null
    ) {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('Transaction amount must be positive');
        }
    }

    /**
     * Convert to array for model creation.
     */
    public function toArray(): array
    {
        return [
            'user_id' => $this->user->id,
            'type' => $this->type,
            'source' => $this->source,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'description' => $this->description,
            'order_id' => $this->order?->id,
            'payment_id' => $this->payment?->id,
        ];
    }

    /**
     * Get formatted amount for display.
     */
    public function getFormattedAmount(): string
    {
        return app(MoneyService::class)->getFormattedAmount($this->amount, $this->currency);
    }

    /**
     * Create a credit transaction DTO.
     */
    public static function credit(
        User $user,
        BalanceTransactionSource $source,
        int $amount,
        string $currency = 'RUB',
        ?string $description = null,
        ?Order $order = null,
        ?Payment $payment = null
    ): self {
        return new self(
            user: $user,
            type: BalanceTransactionType::CREDIT,
            source: $source,
            amount: $amount,
            currency: $currency,
            description: $description,
            order: $order,
            payment: $payment
        );
    }

    /**
     * Create a debit transaction DTO.
     */
    public static function debit(
        User $user,
        BalanceTransactionSource $source,
        int $amount,
        string $currency = 'RUB',
        ?string $description = null,
        ?Order $order = null,
        ?Payment $payment = null
    ): self {
        return new self(
            user: $user,
            type: BalanceTransactionType::DEBIT,
            source: $source,
            amount: $amount,
            currency: $currency,
            description: $description,
            order: $order,
            payment: $payment
        );
    }

    /**
     * Check if this is a credit transaction.
     */
    public function isCredit(): bool
    {
        return $this->type === BalanceTransactionType::CREDIT;
    }

    /**
     * Check if this is a debit transaction.
     */
    public function isDebit(): bool
    {
        return $this->type === BalanceTransactionType::DEBIT;
    }
}
