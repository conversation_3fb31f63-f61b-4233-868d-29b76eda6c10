<?php

namespace App\DTOs\Balance;

use App\Models\Order;
use App\Models\User;
use App\Services\MoneyService;

class CombinedPaymentDTO
{
    /**
     * Data transfer object for combined payment requests (balance + external gateway).
     *
     * @param Order $order The order to pay for
     * @param int $balanceAmount Amount to pay from balance in minor currency units
     * @param int $externalAmount Amount to pay via external gateway in minor currency units
     * @param string $externalPaymentMethod Payment method code for external payment
     * @param array $additionalData Additional data for the external payment gateway
     */
    public function __construct(
        public readonly Order $order,
        public readonly int $balanceAmount,
        public readonly int $externalAmount,
        public readonly string $externalPaymentMethod,
        public readonly array $additionalData = []
    ) {
        if ($balanceAmount < 0 || $externalAmount < 0) {
            throw new \InvalidArgumentException('Payment amounts cannot be negative');
        }

        if ($balanceAmount + $externalAmount <= 0) {
            throw new \InvalidArgumentException('Total payment amount must be positive');
        }

        if ($externalAmount > 0 && empty($externalPaymentMethod)) {
            throw new \InvalidArgumentException('External payment method is required when external amount > 0');
        }

        $totalAmount = $balanceAmount + $externalAmount;
        $orderAmount = $order->total_amount;

        if (abs($totalAmount - $orderAmount) > 0.01) { // Allow small floating point differences
            throw new \InvalidArgumentException('Total payment amount must equal order amount');
        }
    }

    /**
     * Convert to array.
     */
    public function toArray(): array
    {
        return [
            'order_id' => $this->order->id,
            'balance_amount' => $this->balanceAmount,
            'external_amount' => $this->externalAmount,
            'external_payment_method' => $this->externalPaymentMethod,
            'additional_data' => $this->additionalData,
        ];
    }

    /**
     * Get balance amount in minor currency units.
     */
    public function getBalanceAmount(): int
    {
        return $this->balanceAmount;
    }

    /**
     * Get external amount in minor currency units.
     */
    public function getExternalAmount(): int
    {
        return $this->externalAmount;
    }

    /**
     * Get total amount in minor currency units.
     */
    public function getTotalAmount(): int
    {
        return $this->getBalanceAmount() + $this->getExternalAmount();
    }

    /**
     * Get formatted total balance amount for display.
     */
    public function getFormattedTotalAmount(): string
    {
        return app(MoneyService::class)->getFormattedAmount($this->getTotalAmount(), $this->order->currency);
    }


    /**
     * Check if this is a balance-only payment.
     */
    public function isBalanceOnly(): bool
    {
        return $this->externalAmount <= 0;
    }

    /**
     * Check if this is an external-only payment.
     */
    public function isExternalOnly(): bool
    {
        return $this->balanceAmount <= 0;
    }

    /**
     * Check if this is a combined payment.
     */
    public function isCombined(): bool
    {
        return $this->balanceAmount > 0 && $this->externalAmount > 0;
    }

    /**
     * Get the user from the order.
     */
    public function getUser(): User
    {
        return $this->order->user;
    }

    /**
     * Create a balance-only payment DTO.
     */
    public static function balanceOnly(Order $order): self
    {
        $orderAmount = $order->total_amount;

        return new self(
            order: $order,
            balanceAmount: $orderAmount,
            externalAmount: 0,
            externalPaymentMethod: '',
            additionalData: []
        );
    }

    /**
     * Create an external-only payment DTO.
     */
    public static function externalOnly(Order $order, string $paymentMethod, array $additionalData = []): self
    {
        $orderAmount = $order->total_amount;

        return new self(
            order: $order,
            balanceAmount: 0,
            externalAmount: $orderAmount,
            externalPaymentMethod: $paymentMethod,
            additionalData: $additionalData
        );
    }

    /**
     * Create a combined payment DTO.
     */
    public static function combined(
        Order $order,
        int $balanceAmount,
        string $externalPaymentMethod,
        array $additionalData = []
    ): self {
        $orderAmount = $order->total_amount;
        $externalAmount = $orderAmount - $balanceAmount;

        return new self(
            order: $order,
            balanceAmount: $balanceAmount,
            externalAmount: $externalAmount,
            externalPaymentMethod: $externalPaymentMethod,
            additionalData: $additionalData
        );
    }
}
