<?php

namespace App\Services;

use Cknow\Money\Money;

class MoneyService
{
    /**
     * Get amount in major currency units (e.g., rubles, dollars) as a Money object.
     *
     * @param int $amount Amount in minor currency units (e.g., kopecks, cents)
     * @param string $currency Currency code (e.g., 'RUB', 'USD')
     * @param string $locale Locale code (e.g., 'en_US', 'ru_RU')
     *
     * @return Money Money object
     */
    public function getAmountInMajorUnits($amount, string $currency = 'RUB', $locale = 'en_US'): Money
    {
       if (! Money::isValidCurrency($currency)) {
           throw new \InvalidArgumentException('Invalid currency: ' . $currency);
       }

        return Money::parse($amount, $currency, false, $locale);
    }

    /**
     * Get formatted amount for display.
     *
     * @param int $amount Amount in minor currency units (e.g., kopecks, cents)
     * @param string $currency Currency code (e.g., 'RUB', 'USD')
     * @param string $locale Locale code (e.g., 'en_US', 'ru_RU')
     *
     * @return string Formatted amount with currency symbol
     */
    public function getFormattedAmount(int $amount, string $currency = 'RUB', $locale = 'en_US'): string
    {
        $money = $this->getAmountInMajorUnits($amount, $currency, $locale);

        return $money->format($locale);
    }
}
