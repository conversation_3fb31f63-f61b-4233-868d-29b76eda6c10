<?php

namespace App\Services\Leadteh;

use App\Exceptions\Leadteh\LeadtehApiException;
use App\Exceptions\Leadteh\LeadtehRateLimitException;
use App\Exceptions\Leadteh\LeadtehAccountException;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class LeadtehService
{
    private string $apiToken;
    private string $baseUrl;
    private int $rateLimitDelay;
    private static int $lastRequestTime = 0;
    private bool $refEnabled;
    private array $refererLevels = [];
    private int $refDepth;
    private int $refMinAmount;

    public function __construct()
    {
        $this->apiToken = config('services.leadteh.api_token');

        if (!$this->apiToken) {
            throw new LeadtehApiException('LeadTeh API token not configured');
        }

        $this->baseUrl = config('services.leadteh.base_url');
        $this->rateLimitDelay = config('services.leadteh.rate_limit_delay');
        $this->refEnabled = (bool) config('services.leadteh.ref_enabled', false);
        $this->refererLevels = config('services.leadteh.ref_levels', []);
        $this->refMinAmount = (int) config('services.leadteh.ref_min_amount', 1000);
        $this->refDepth = count($this->refererLevels); // сумма в копейках, 10 руб = 1000 коп

    }

    /**
     * Выполнить HTTP запрос к API LeadTeh с rate limiting
     */
    private function makeRequest(string $method, string $endpoint, array $data = []): ?array
    {
        $this->enforceRateLimit();

        $url = $this->baseUrl . ltrim($endpoint, '/');
        $params = array_merge($data, ['api_token' => $this->apiToken]);

        Log::info('LeadTeh API Request', [
            'method' => $method,
            'url' => $url,
            'params' => array_merge($params, ['api_token' => '[HIDDEN]'])
        ]);

        $response = Http::withHeaders([
            'X-Requested-With' => 'XMLHttpRequest',
        ])->$method($url, $params);

        if ($response->status() === 429) {
            throw new LeadtehRateLimitException();
        }

        if (!$response->successful()) {
            Log::error('LeadTeh API Error', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            throw new LeadtehApiException(
                'API request failed: ' . $response->body(),
                $response->status(),
                $response->json() ?? []
            );
        }

        $result = $response->json();
        Log::info('LeadTeh API Response', ['result' => $result]);

        return $result;
    }

    /**
     * Обеспечить соблюдение rate limit (2 запроса в секунду)
     */
    private function enforceRateLimit(): void
    {
        $currentTime = microtime(true) * 1000;
        $timeSinceLastRequest = $currentTime - self::$lastRequestTime;

        if ($timeSinceLastRequest < $this->rateLimitDelay) {
            $sleepTime = $this->rateLimitDelay - $timeSinceLastRequest;
            usleep($sleepTime * 1000);
        }

        self::$lastRequestTime = microtime(true) * 1000;
    }

    /**
     * Получить информацию об аккаунте
     */
    public function getMe(): array
    {
        return $this->makeRequest('get', 'getMe');
    }

    /**
     * Получить список контактов
     */
    public function getContacts(array $filters = []): array
    {
        return $this->makeRequest('get', 'getContacts', $filters);
    }

    /**
     * Отправить сообщение контакту
     */
    public function sendMessage(int $contactId, string $message, ?int $botId = null): array
    {
        $data = [
            'contact_id' => $contactId,
            'text' => $message,
        ];

        if ($botId) {
            $data['bot_id'] = $botId;
        }

        return $this->makeRequest('post', 'sendMessage', $data);
    }

    /**
     * Отправить сообщение в WhatsApp по номеру телефона
     */
    public function sendMessageToWhatsApp(string $phone, string $message, int $botId, ?string $name = null): array
    {
        $data = [
            'phone' => $phone,
            'text' => $message,
            'bot_id' => $botId,
        ];

        if ($name) {
            $data['name'] = $name;
        }

        return $this->makeRequest('post', 'sendMessageToWhatsApp', $data);
    }

    /**
     * Получить счета контакта
     */
    public function getContactAccounts(int $contactId): array
    {
        return $this->makeRequest('get', 'getContactAccounts', [
            'contact_id' => $contactId
        ]);
    }

    /**
     * Создать счет для контакта
     */
    public function addContactAccount(int $contactId, string $currency): array
    {
        return $this->makeRequest('post', 'addContactAccount', [
            'contact_id' => $contactId,
            'currency' => $currency,
        ]);
    }

    /**
     * Удалить счет контакта
     */
    public function deleteContactAccount(int $accountId): ?array
    {
        return $this->makeRequest('post', 'deleteContactAccount', [
            'account_id' => $accountId,
        ]);
    }

    /**
     * Начислить средства на счет контакта
     *
     * @param int $accountId ID счета
     * @param int $amount Сумма в копейках
     * @param string $description Описание транзакции, количество символов в поле description не может превышать 255.
     */
    public function addFundsToContactAccount(int $accountId, int $amount, string $description): array
    {
        if (mb_strlen($description) > 255) {
            throw new LeadtehApiException('Account Description length must not exceed 255 characters');
        }

        return $this->makeRequest('post', 'addFundsToContactAccount', [
            'account_id' => $accountId,
            'amount' => $amount,
            'description' => $description,
        ]);
    }

    /**
     * Списать средства со счета контакта
     *
     * @param int $accountId ID счета
     * @param int $amount Сумма в копейках
     * @param string $description Описание транзакции, количество символов в поле description не может превышать 255.
     */
    public function withdrawFundsFromContactAccount(int $accountId, int $amount, string $description): ?array
    {
        if (mb_strlen($description) > 255) {
            throw new LeadtehApiException('Account Description length must not exceed 255 characters');
        }

        return $this->makeRequest('post', 'withdrawFundsFromContactAccount', [
            'account_id' => $accountId,
            'amount' => $amount,
            'description' => $description,
        ]);
    }

    /**
     * Получить цепочку рефереров контакта
     */
    public function getReferrers(int $contactId, int $depth = 3, bool $isFlat = false): array
    {
        return $this->makeRequest('get', 'getReferrers', [
            'contact_id' => $contactId,
            'depth' => $depth,
            'is_flat' => $isFlat ? 1 : 0,
        ]);
    }

    /**
     * Установить переменную контакта
     */
    public function setContactVariable(int $contactId, string $name, mixed $value, int $deletable = 0): array
    {
        // convert $value to string if it is different
        if (!is_string($value)) {
            $value = json_encode($value);
        }

        return $this->makeRequest('post', 'setContactVariable', [
            'contact_id' => $contactId,
            'name' => $name,
            'value' => $value,
            'deletable' => $deletable,
        ]);
    }

    /**
     * Добавить тег к контакту
     */
    public function attachTagToContact(int $contactId, string $name): ?array
    {
        return $this->makeRequest('post', 'attachTagToContact', [
            'contact_id' => $contactId,
            'name' => $name,
        ]);
    }

    /**
     * Удалить тег у контакта
     */
    public function detachTagFromContact(int $contactId, string $name): ?array
    {
        return $this->makeRequest('get', 'detachTagFromContact', [
            'contact_id' => $contactId,
            'name' => $name,
        ]);
    }

    /**
     * Получить теги контакта
     */
    public function getContactTags(int $contactId): array
    {
        return $this->makeRequest('get', 'getContactTags', [
            'contact_id' => $contactId,
        ]);
    }

    /**
     * Получить переменные контакта
     */
    public function getContactVariables(int $contactId): array
    {
        return $this->makeRequest('get', 'getContactVariables', [
            'contact_id' => $contactId,
        ]);
    }

    /**
     * Удалить переменную контакта
     */
    public function deleteContactVariable(int $contactId, string $name): ?array
    {
        return $this->makeRequest('post', 'deleteContactVariable', [
            'contact_id' => $contactId,
            'name' => $name,
        ]);
    }

    /**
     * Создать или обновить контакт
     */
    public function createOrUpdateContact(array $data): array
    {
        return $this->makeRequest('post', 'createOrUpdateContact', $data);
    }

    /**
     * Получить рефералы контакта
     */
    public function getReferrals(int $contactId, array $filters = [], int $page = 1): array
    {
        $data = [
            'contact_id' => $contactId,
            'page' => $page,
        ];

        if (!empty($filters)) {
            $data['filters'] = $filters;
        }

        return $this->makeRequest('post', 'getReferrals', $data);
    }

    /**
     * Получить количество рефералов всей сети контакта
     */
    public function getCountReferrals(int $contactId, array $filters = []): array
    {
        $data = [
            'contact_id' => $contactId,
        ];

        if (!empty($filters)) {
            $data['filters'] = $filters;
        }

        return $this->makeRequest('post', 'getCountReferrals', $data);
    }

    /**
     * Создать счет с уведомлением
     */
    public function createAccountWithNotification(int $contactId, string $currency): array
    {
        $result = $this->addContactAccount($contactId, $currency);

        $this->sendMessage($contactId, "Создан новый счет: {$currency}");

        return $result;
    }

    /**
     * Создать операции для отката начисления вознаграждений
     *
     * @param int $amount
     * @param int $contactId
     * @param array $additionalData
     * @param string $reasonText
     * @return array ['status' => 'success', 'contact_id' => $contactId, 'amount' => $amount, 'rewards' => $rewards, 'total_referrers' => count($rewards), 'rollback_operations' => $rollbackOperations]
     */
    private function prepareRollbackOperations(int $amount, int $contactId, array $additionalData = [], string $reasonText = ''): array
    {
        $dateText = Carbon::now()->format('H:i') . ": ";
        $amountInMajor = number_format($amount / 100, 2);

        $rollbackOperations = [];
        $referrersResponse = $this->getReferrers($contactId, $this->refDepth);
        $referrers = [];
        if (isset($referrersResponse['data'])) {
            // Извлечь рефереров из дерева
            $current = $referrersResponse['data'];
            $level = 1;
            while (isset($current['referrer']) && $level <= $this->refDepth) {
                $referrers[$level] = $current['referrer'];
                $current = $current['referrer'];
                $level++;
            }
        }

        $level = 1;
        foreach ($referrers as $referrer) {
            if (!isset($this->refererLevels[$level])) continue;

            $rewardPercent = $this->refererLevels[$level];
            $rewardAmount = ($amount * $rewardPercent) / 100;
            $referrerId = $referrer['id'];

            // Получить список счетов для поиска ID счета RUB
            $accounts = $this->getContactAccounts($referrerId);
            $rubAccountId = null;

            if (isset($accounts['data'])) {
                foreach ($accounts['data'] as $account) {
                    if ($account['currency'] === 'RUB') {
                        $rubAccountId = $account['id'];
                        break;
                    }
                }
            }

            if (!$rubAccountId) {
                Log::error('RUB account not found for referrer', ['referrer_id' => $referrerId]);
                continue;
            }

            $paymentId = $additionalData['payment_id'] ?? 'unknown';
            $paymentDate = $additionalData['payment_created_at'] ? Carbon::parse($additionalData['payment_created_at'])->format('d.m.Y H:i:s') : 'unknown';
            $rewardAmountInMajor = $this->getAmountInMajor($rewardAmount);
            $description = $dateText . "Вознаграждение {$rewardAmountInMajor} руб ({$rewardPercent}%) за совершенную покупку {$amountInMajor} рефералом {$contactId} - уровень: {$level}, PaymentId: {$paymentId} от {$paymentDate}";

            $rollbackOperations[] = [
                'type' => 'withdraw',
                'account_id' => $rubAccountId,
                'amount' => $rewardAmount,
                'description' => $dateText . "Откат начисления из-за ошибки в реферальной цепочке. Причина: {$reasonText}. Исходная операция: {$description}"
            ];
        }

        return $rollbackOperations;
    }

    /**
     * Откатить начисление вознаграждений. Обратная операция для processReferralRewards
     *
     * @param int $amount Сумма в копейках, от которой начислялись вознаграждения
     * @param int $contactId ID реферала
     * @param array $additionalData Дополнительные данные для описания операции
     * @param string $reasonText Причина отката
     */
    public function rollbackReferralRewards(int $amount, int $contactId, array $additionalData = [], string $reasonText = '')
    {
        // Получить данные для операции отката
        $rollbackOperations = $this->prepareRollbackOperations($amount, $contactId, $additionalData, $reasonText);
        // Выполнить откат
        $this->processRollback($rollbackOperations);

        return [
            'status' => 'success',
            'contact_id' => $contactId,
            'amount' => $amount,
            'total_referrers' => count($rollbackOperations),
            'rollback_operations' => $rollbackOperations
        ];
    }

    /**
     * Обработать реферальные вознаграждения
     *
     * @param int $amount Сумма в копейках, на основе которой начисляются вознаграждения
     * @param int $contactId ID реферала
     * @param array $additionalData Дополнительные данные для описания операции
     *
     * @return array ['status' => 'success', 'contact_id' => $contactId, 'amount' => $amount, 'rewards' => $rewards, 'total_referrers' => count($rewards), 'rollback_operations' => $rollbackOperations]
     */
    public function processReferralRewards(int $amount, int $contactId, array $additionalData = []): array
    {
        $dateText = Carbon::now()->format('H:i') . ": ";
        $amountInMajor = number_format($amount / 100, 2);

        if (!$this->isReferralEnabled()) {
            Log::info('Referral system disabled', ['contact_id' => $contactId]);
            return ['status' => 'disabled', 'message' => 'Referral system is disabled'];
        }

        $minAmount = $this->refMinAmount;
        if ($amount < $minAmount) {
            Log::info('Amount below minimum for referral', [
                'amount' => $amount,
                'min_amount' => $minAmount,
                'contact_id' => $contactId
            ]);
            return ['status' => 'below_minimum', 'amount' => $amount, 'min_amount' => $minAmount];
        }

        Log::info('Processing referral rewards', [
            'amount' => $amount,
            'contact_id' => $contactId,
            'additional_data' => $additionalData
        ]);

        $rollbackOperations = [];

        try {
            // Получить цепочку рефереров
            $referrersResponse = $this->getReferrers($contactId, $this->refDepth);

            // Обработать ответ в зависимости от структуры
            $referrers = [];
            if (isset($referrersResponse['data'])) {
                // Извлечь рефереров из дерева
                $current = $referrersResponse['data'];
                $level = 1;
                while (isset($current['referrer']) && $level <= $this->refDepth) {
                    $referrers[$level] = $current['referrer'];
                    $current = $current['referrer'];
                    $level++;
                }
            }

            if (empty($referrers)) {
                Log::info('No referrers found', ['contact_id' => $contactId]);
                return ['status' => 'no_referrers', 'contact_id' => $contactId];
            }

            $rewards = [];

            // Обработать каждый уровень
            foreach ($referrers as $level => $referrer) {
                if (!isset($this->refererLevels[$level])) continue;

                $rewardPercent = $this->refererLevels[$level];
                $rewardAmount = ($amount * $rewardPercent) / 100;
                $referrerId = $referrer['id'];

                Log::info('Processing referral reward', [
                    'level' => $level,
                    'referrer_id' => $referrerId,
                    'reward_percent' => $rewardPercent,
                    'reward_amount' => $rewardAmount
                ]);

                // Создать или получить счет "RUB" для реферальных вознаграждений
                try {
                    $this->addContactAccount($referrerId, 'RUB');
                } catch (LeadtehApiException $e) {
                    // Счет уже существует, продолжаем
                }

                // Получить список счетов для поиска ID счета RUB
                $accounts = $this->getContactAccounts($referrerId);
                $rubAccountId = null;

                if (isset($accounts['data'])) {
                    foreach ($accounts['data'] as $account) {
                        if ($account['currency'] === 'RUB') {
                            $rubAccountId = $account['id'];
                            break;
                        }
                    }
                }

                if (!$rubAccountId) {
                    Log::error('RUB account not found for referrer', ['referrer_id' => $referrerId]);
                    continue;
                }

                // Начислить вознаграждение (сумма в копейках)
                $paymentId = $additionalData['payment_id'] ?? 'unknown';
                $paymentDate = $additionalData['payment_created_at'] ? Carbon::parse($additionalData['payment_created_at'])->format('d.m.Y H:i:s') : 'unknown';
                $rewardAmountInMajor = $this->getAmountInMajor($rewardAmount);
                $description = $dateText . "Вознаграждение {$rewardAmountInMajor} руб ({$rewardPercent}%) за совершенную покупку {$amountInMajor} рефералом {$contactId} - уровень: {$level}, PaymentId: {$paymentId} от {$paymentDate}";

                $creditResult = $this->addFundsToContactAccount($rubAccountId, $rewardAmount, $this->truncateDescription($description));

                $rollbackOperations[] = [
                    'type' => 'withdraw',
                    'account_id' => $rubAccountId,
                    'amount' => $rewardAmount,
                    'description' => $dateText . "Автоматический откат начисления из-за ошибки в реферальной цепочке. Исходная операция: {$description}"
                ];

                // Установить переменные
                $this->setContactVariable($referrerId, 'last_referral_reward', $rewardAmount);
                $this->setContactVariable($referrerId, 'last_referral_date', now()->toDateTimeString());
                $this->setContactVariable($referrerId, 'referral_from_contact', $contactId);

                $rewards[] = [
                    'level' => $level,
                    'referrer_id' => $referrerId,
                    'amount' => $rewardAmount,
                    'percent' => $rewardPercent,
                    'result' => $creditResult
                ];
            }

            // Отправить уведомления (только после успешного завершения всех операций)
            $this->notifyReferralChain($contactId, $rewards, $additionalData);

            Log::info('Referral rewards processed successfully', [
                'contact_id' => $contactId,
                'total_rewards' => count($rewards),
                'rewards' => $rewards
            ]);

            return [
                'status' => 'success',
                'contact_id' => $contactId,
                'amount' => $amount,
                'rewards' => $rewards,
                'total_referrers' => count($rewards),
                'rollback_operations' => $rollbackOperations
            ];

        } catch (Exception $e) {
            Log::error('Error processing referral rewards, rolling back', [
                'contact_id' => $contactId,
                'error' => $e->getMessage(),
                'rollback_operations' => count($rollbackOperations)
            ]);

            // Откатить все операции
            $this->processRollback($rollbackOperations);

            throw new LeadtehAccountException(
                'Failed to process referral rewards: ' . $e->getMessage(),
                0,
                ['contact_id' => $contactId, 'amount' => $amount]
            );
        }
    }


    private function processRollback(array $rollbackOperations): void
    {
        foreach ($rollbackOperations as $operation) {
            try {
                if ($operation['type'] === 'withdraw') {
                    $this->withdrawFundsFromContactAccount(
                        $operation['account_id'],
                        $operation['amount'],
                        $this->truncateDescription($operation['description'])
                    );
                }
            } catch (Exception $rollbackError) {
                Log::error('Rollback operation failed', [
                    'operation' => $operation,
                    'error' => $rollbackError->getMessage()
                ]);
            }
        }
    }

    /**
     * Уведомить цепочку рефереров
     */
    public function notifyReferralChain(int $contactId, array $rewards, array $additionalData = []): void
    {
        foreach ($rewards as $reward) {
            $rewardAmountInMajor = $this->getAmountInMajor($reward['amount']);
            $message = "🎉 Вы получили реферальное вознаграждение {$rewardAmountInMajor} руб. за активность вашего реферала!";

            try {
                $this->sendMessage($reward['referrer_id'], $message);
                Log::info('Referral notification sent', [
                    'referrer_id' => $reward['referrer_id'],
                    'amount' => $reward['amount']
                ]);
            } catch (Exception $e) {
                Log::error('Failed to send referral notification', [
                    'referrer_id' => $reward['referrer_id'],
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Проверить, включена ли реферальная система
     */
    private function isReferralEnabled(): bool
    {
        return $this->refEnabled;
    }

    public function getReferralSettings(): array
    {
        return [
            'ref_enabled' => $this->refEnabled,
            'ref_levels' => $this->refererLevels,
            'ref_depth' => $this->refDepth,
            'ref_min_amount' => $this->refMinAmount,
        ];
    }

    private function getAmountInMajor(int $amount): string
    {
        return number_format($amount / 100, 2);
    }

    /***
     * Обрезать описание до 255 символов
     */
    private function truncateDescription(string $description): string
    {
        if (mb_strlen($description) > 255) {
            return mb_substr($description, 0, 252) . '...';
        }

        return $description;
    }
}
