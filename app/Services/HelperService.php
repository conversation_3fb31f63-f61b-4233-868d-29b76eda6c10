<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class HelperService
{
    /**
     * Validate UUID format.
     */
    public function isValidUuid(string $uuid): bool
    {
        return Str::isUuid($uuid);
    }

    /**
     * Detect if request is from a browser.
     */
    public function isBrowserUserAgent(Request $request): bool
    {
        $userAgent = $request->userAgent() ?? '';
        $userAgentLower = strtolower($userAgent);

        // Non-browser keywords: v2ray clients and technical HTTP clients
        $nonBrowserKeywords = [
            // v2ray and derivatives
            'xray',
            'v2ray',
            'v2raya',
            'v2raytun',
            '2rayng',
            '2rayu',
            'hiddify',
            'v3ray',
            'sing-box',
            'happ',
            // technical clients
            'curl',
            'wget',
            'httpclient',
            'python-requests',
            'go-http-client',
            'okhttp',
            'postman',
            'axios',
            'node-fetch',
            'java/', // often in UA from Java clients
            'libwww',
            'perl',
        ];

        // Browser keywords - identify by typical browser signatures
        $browserKeywords = [
            'mozilla/',     // common identifier, almost all browsers
            'chrome/',      // Chrome and Edge
            'safari/',      // Safari
            'firefox/',     // Firefox
            'opera/',       // Opera
            'edg/',         // Microsoft Edge
            'instagram',    // WebView Instagram
            'facebook',     // Facebook in-app browser
            'telegram',     // Telegram in-app browser
            'whatsapp',     // WhatsApp WebView
            'tiktok',       // TikTok in-app browser
        ];

        // If user-agent is empty, consider it not a browser
        if (empty($userAgentLower)) {
            return false;
        }

        // If contains one of non-browser signatures - it's not a browser
        foreach ($nonBrowserKeywords as $keyword) {
            if (strpos($userAgentLower, $keyword) !== false) {
                return false;
            }
        }

        // If contains at least one browser signature - it's a browser
        foreach ($browserKeywords as $keyword) {
            if (strpos($userAgentLower, $keyword) !== false) {
                return true;
            }
        }

        // Everything else - also not a browser
        return false;
    }

    /**
     * Detect if request is from a mobile browser.
     */
    public function isMobileBrowser(Request $request): bool
    {
        $userAgent = strtolower($request->userAgent() ?? '');

        // Quick filter: User-Agent should contain browser engine
        $isBrowserEngine = preg_match('/webkit|khtml|gecko|trident|blink/', $userAgent);

        // Should indicate mobile device
        $isMobileDevice = preg_match('/android|iphone|ipad|ipod|iemobile|blackberry|mobile/', $userAgent);

        // Should not be a known app
        $knownAppAgents = [
            'v2ray', 'cfnetwork', 'okhttp', 'curl', 'wget', 'python', 'java', 'libhttp', 'go-http-client',
            'postman', 'axios', 'httpclient', 'nodejs', 'dart', 'electron', 'reactnative', 'flutter'
        ];

        foreach ($knownAppAgents as $appSig) {
            if (str_contains($userAgent, $appSig)) {
                return false;
            }
        }

        return $isBrowserEngine && $isMobileDevice;
    }

    /**
     * Replace variables in text with user-specific values.
     */
    public function replaceTextVariables(string $text, User $user): string
    {
        return str_replace(
            [
                '{uuid}',
                '{client_id}',
                '{host}',
                '{origin}',
                '{app_url}',
                '{APP_URL}',
            ],
            [
                $user->id,
                $user->client_id,
                request()->getHost(),
                request()->url(),
                config('app.url'),
                config('app.url'),
            ],
            $text
        );
    }

    /**
     * Encode JSON string safely for UTF-8.
     */
    public function encodeJsonUtf8Safe(string $jsonString): ?string
    {
        $decoded = json_decode($jsonString, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return null;
        }

        return json_encode($decoded);
    }

    /**
     * Get support text with user variables replaced.
     */
    public function getSupportText(User $user): string
    {
        $text = config('app.support_text', 'Hello, I have a problem with my VPN connection. My ID: {client_id}');
        return str_replace('{client_id}', $user->client_id, $text);
    }

    /**
     * Get Telegram support deep link.
     */
    public function getTelegramSupportDeepLink(User $user): string
    {
        $supportUsername = config('app.telegram_support', '#');
        $supportText = $this->getSupportText($user);
        $supportLink = 'tg://resolve?domain={username}&text={text}';

        return str_replace(
            ['{username}', '{text}'],
            [$supportUsername, $supportText],
            $supportLink
        );
    }

    /**
     * Get WhatsApp support deep link.
     */
    public function getWhatsappSupportDeepLink(User $user): string
    {
        $supportPhoneNumber = config('app.whatsapp_support_phone', '#');
        $supportText = $this->getSupportText($user);
        $supportLink = 'whatsapp://send/?phone={phone}&text={text}';

        return str_replace(
            ['{phone}', '{text}'],
            [urlencode($supportPhoneNumber), urlencode($supportText)],
            $supportLink
        );
    }

    /**
     * Get Telegram support link (web version).
     */
    public function getTelegramSupportLink(User $user): string
    {
        $supportUsername = config('app.telegram_support', '#');
        $supportText = $this->getSupportText($user);
        $supportLink = 'https://t.me/{username}?text={text}';

        return str_replace(
            ['{username}', '{text}'],
            [$supportUsername, $supportText],
            $supportLink
        );
    }

    /**
     * Get WhatsApp support link (web version).
     */
    public function getWhatsappSupportLink(User $user): string
    {
        $supportPhoneNumber = config('app.whatsapp_support_phone', '#');
        $supportText = $this->getSupportText($user);
        $supportLink = 'https://api.whatsapp.com/send?phone={phone}&text={text}';

        return str_replace(
            ['{phone}', '{text}'],
            [urlencode($supportPhoneNumber), urlencode($supportText)],
            $supportLink
        );
    }

    /**
     * Russian pluralization helper.
     */
    public function pluralize(int $number, string $one, string $few, string $many): string
    {
        $mod10 = $number % 10;
        $mod100 = $number % 100;

        if ($mod100 >= 11 && $mod100 <= 19) {
            return $many;
        }

        if ($mod10 == 1) {
            return $one;
        }

        if ($mod10 >= 2 && $mod10 <= 4) {
            return $few;
        }

        return $many;
    }

    /**
     * Format remaining time in a human-readable format.
     */
    public function formatRemainingTime(\Carbon\Carbon $now, \Carbon\Carbon $expiry): string
    {
        $diff = $now->diff($expiry);

        if ($diff->d > 0) {
            return $diff->d . ' ' . $this->pluralize($diff->d, 'день', 'дня', 'дней');
        } elseif ($diff->h > 0) {
            return $diff->h . ' ' . $this->pluralize($diff->h, 'час', 'часа', 'часов')
                . ' ' . $diff->i . ' ' . $this->pluralize($diff->i, 'минута', 'минуты', 'минут');
        } else {
            return $diff->i . ' ' . $this->pluralize($diff->i, 'минута', 'минуты', 'минут');
        }
    }
}
