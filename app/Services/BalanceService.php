<?php

namespace App\Services;

use App\Enums\BalanceTransactionSource;
use App\Enums\BalanceTransactionType;
use App\Events\Balance\BalanceTransactionCreated;
use App\Models\BalanceTransaction;
use App\Models\Order;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BalanceService
{
    /**
     * Get current user balance in minor currency units (e.g., kopecks, cents).
     */
    public function getCurrentUserBalance(User $user): int
    {
        return $this->calculateActualBalance($user);
    }

    /**
     * Get actual user balance from balance transactions (minor units).
     *
     * @param User $user
     * @return int
     */
    public function calculateActualBalance(User $user): int
    {
        return $user->getTotalBalanceAmount();
    }

    /**
     * Get actual user balance from transactions (major currency units).
     *
     * @param User $user
     * @return float
     */
    public function calculateActualBalanceInMajorUnits(User $user): float
    {
        return $this->calculateActualBalance($user) / 100;
    }

    /**
     * Add funds to user balance.
     *
     * @param User $user The user to add funds to
     * @param float $amount Amount in major currency units
     * @param BalanceTransactionSource $source Source of the funds
     * @param string|null $description Optional description
     * @param Order|null $order Related order if applicable
     * @param Payment|null $payment Related payment if applicable
     * @return BalanceTransaction The created transaction record
     * @throws \Exception If the transaction fails
     */
    public function addFunds(
        User $user,
        float $amount,
        BalanceTransactionSource $source,
        ?string $description = null,
        ?Order $order = null,
        ?Payment $payment = null
    ): BalanceTransaction {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('Amount must be positive');
        }

        $amountInMinorUnits = (int) round($amount * 100);

        return DB::transaction(function () use ($user, $amountInMinorUnits, $source, $description, $order, $payment) {
            // Lock the user record to prevent race conditions
            $user = User::lockForUpdate()->find($user->id);

            $balanceBefore = $user->getTotalBalanceAmount();
            $balanceAfter = $balanceBefore + $amountInMinorUnits;

            // Create transaction record
            $transaction = BalanceTransaction::create([
                'user_id' => $user->id,
                'type' => BalanceTransactionType::CREDIT,
                'source' => $source,
                'amount' => $amountInMinorUnits,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'description' => $description,
                'order_id' => $order?->id,
                'payment_id' => $payment?->id,
            ]);

            Log::info('Balance credited', [
                'user_id' => $user->id,
                'amount' => $amountInMinorUnits,
                'source' => $source->value,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'transaction_id' => $transaction->id,
            ]);

            // Dispatch balance transaction created event
            BalanceTransactionCreated::dispatch($transaction);

            return $transaction;
        });
    }

    /**
     * Deduct funds from user balance.
     *
     * @param User $user The user to deduct funds from
     * @param float $amount Amount in major currency units
     * @param BalanceTransactionSource $source Source of the deduction
     * @param string|null $description Optional description
     * @param Order|null $order Related order if applicable
     * @param Payment|null $payment Related payment if applicable
     * @return BalanceTransaction The created transaction record
     * @throws \Exception If insufficient balance or transaction fails
     */
    public function deductFunds(
        User $user,
        float $amount,
        BalanceTransactionSource $source,
        ?string $description = null,
        ?Order $order = null,
        ?Payment $payment = null
    ): BalanceTransaction {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('Amount must be positive');
        }

        $amountInMinorUnits = (int) round($amount * 100);

        return DB::transaction(function () use ($user, $amountInMinorUnits, $source, $description, $order, $payment) {
            // Lock the user record to prevent race conditions
            $user = User::lockForUpdate()->find($user->id);

            $balanceBefore = $user->getTotalBalanceAmount();
            $balanceAfter = $balanceBefore - $amountInMinorUnits;

            // Check if user has sufficient balance (allow negative balance for flexibility)
            // This check can be customized based on business rules
            if ($balanceAfter < -1000000) { // Allow up to -10,000 in major units as overdraft
                throw new \Exception('Insufficient balance. Maximum overdraft exceeded.');
            }

            // Create transaction record
            $transaction = BalanceTransaction::create([
                'user_id' => $user->id,
                'type' => BalanceTransactionType::DEBIT,
                'source' => $source,
                'amount' => $amountInMinorUnits,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'description' => $description,
                'order_id' => $order?->id,
                'payment_id' => $payment?->id,
            ]);

            Log::info('Balance debited', [
                'user_id' => $user->id,
                'amount' => $amountInMinorUnits,
                'source' => $source->value,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'transaction_id' => $transaction->id,
            ]);

            // Dispatch balance transaction created event
            BalanceTransactionCreated::dispatch($transaction);

            return $transaction;
        });
    }

    /**
     * Check if user can afford a specific amount.
     *
     * @param User $user The user to check
     * @param float $amount Amount in major currency units
     * @return bool True if user can afford the amount
     */
    public function canAfford(User $user, float $amount): bool
    {
        if ($amount <= 0) {
            return true;
        }

        $amountInMinorUnits = (int) round($amount * 100);
        $balanceAfterDeduction = $user->getTotalBalanceAmount() - $amountInMinorUnits;

        // Allow some overdraft (same limit as in deductFunds)
        return $balanceAfterDeduction >= -1000000;
    }

    /**
     * Get user's balance transaction history.
     *
     * @param User $user The user to get history for
     * @param int|null $limit Maximum number of transactions to return
     * @return Collection<BalanceTransaction>
     */
    public function getTransactionHistory(User $user, ?int $limit = null): Collection
    {
        $query = $user->balanceTransactions()
            ->with(['order', 'payment'])
            ->recent();

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Calculate how much of an order amount can be covered by user's balance.
     *
     * @param User $user The user
     * @param float $orderAmount Order amount in major currency units
     * @return array{can_cover_full: bool, available_amount: float, remaining_amount: float}
     */
    public function calculateBalanceCoverage(User $user, float $orderAmount): array
    {
        $userBalance = $user->getTotalBalanceAmount();
        $canCoverFull = $userBalance >= $orderAmount;
        $availableAmount = min($userBalance, $orderAmount);
        $remainingAmount = max(0, $orderAmount - $userBalance);

        return [
            'can_cover_full' => $canCoverFull,
            'available_amount' => $availableAmount,
            'remaining_amount' => $remainingAmount,
        ];
    }

    /**
     * Rollback a balance transaction (create compensating transaction).
     *
     * @param BalanceTransaction $originalTransaction The transaction to rollback
     * @param string $reason Reason for the rollback
     * @return BalanceTransaction The compensating transaction
     */
    public function rollbackTransaction(BalanceTransaction $originalTransaction, string $reason): BalanceTransaction
    {
        $oppositeType = $originalTransaction->type === BalanceTransactionType::CREDIT
            ? BalanceTransactionType::DEBIT
            : BalanceTransactionType::CREDIT;

        $description = "Rollback of transaction {$originalTransaction->id}: {$reason}";

        if ($oppositeType === BalanceTransactionType::CREDIT) {
            return $this->addFunds(
                $originalTransaction->user,
                $originalTransaction->amount / 100,
                BalanceTransactionSource::MANUAL_ADJUSTMENT,
                $description,
                $originalTransaction->order,
                $originalTransaction->payment
            );
        } else {
            return $this->deductFunds(
                $originalTransaction->user,
                $originalTransaction->amount / 100,
                BalanceTransactionSource::MANUAL_ADJUSTMENT,
                $description,
                $originalTransaction->order,
                $originalTransaction->payment
            );
        }
    }
}
