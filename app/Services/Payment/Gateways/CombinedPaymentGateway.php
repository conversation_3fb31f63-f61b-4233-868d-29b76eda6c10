<?php

namespace App\Services\Payment\Gateways;

use App\DTOs\Balance\CombinedPaymentDTO;
use App\Enums\BalanceTransactionSource;
use App\Models\Order;
use App\Models\User;
use App\Services\BalanceService;
use App\Services\Payment\AbstractPaymentGateway;
use App\Services\Payment\PaymentResult;
use App\Services\Payment\WebhookResult;
use App\Services\PaymentService;
use Illuminate\Support\Facades\DB;

/**
 * Combined Payment Gateway - handles payments using both user balance and external gateway.
 */
class CombinedPaymentGateway extends AbstractPaymentGateway
{
    private BalanceService $balanceService;
    private PaymentService $paymentService;

    public function __construct()
    {
        $this->balanceService = app(BalanceService::class);
        $this->paymentService = app(PaymentService::class);
    }

    public function getName(): string
    {
        return 'combined';
    }

    /**
     * Create a combined payment (balance + external gateway).
     *
     * @param array $data Payment data containing CombinedPaymentDTO data
     * @return PaymentResult
     */
    public function createPayment(array $data): PaymentResult
    {
        try {
            $this->log('info', 'Creating combined payment', $data);

            // Validate required data
            $orderId = $data['order_id'] ?? throw new \InvalidArgumentException('order_id is required');
            $balanceAmount = $data['balance_amount'] ?? 0; // in major units
            $externalAmount = $data['external_amount'] ?? 0; // in major units
            $externalMethod = $data['external_payment_method'] ?? null;
            $userId = $data['user_id'] ?? throw new \InvalidArgumentException('user_id is required');

            if ($balanceAmount <= 0 && $externalAmount <= 0) {
                throw new \InvalidArgumentException('At least one payment amount must be positive');
            }

            if ($externalAmount > 0 && empty($externalMethod)) {
                throw new \InvalidArgumentException('External payment method required when external amount > 0');
            }

            // Get user and order
            $user = User::findOrFail($userId);
            $order = Order::findOrFail($orderId);

            // Check if user can afford the balance portion
            if ($balanceAmount > 0 && !$this->balanceService->canAfford($user, $balanceAmount)) {
                $this->log('warning', 'Insufficient balance for combined payment', [
                    'user_id' => $userId,
                    'required_balance_amount' => $balanceAmount,
                    'user_balance' => $this->balanceService->getCurrentUserBalance($user),
                ]);

                return PaymentResult::failure(
                    message: 'Insufficient balance for combined payment',
                    errorCode: 'INSUFFICIENT_BALANCE'
                );
            }

            return DB::transaction(function () use (
                $user, $order, $balanceAmount, $externalAmount, $externalMethod, $orderId, $data
            ) {
                $balanceTransaction = null;
                $externalPaymentResult = null;

                try {
                    // Step 1: Deduct balance if needed
                    if ($balanceAmount > 0) {
                        $balanceTransaction = $this->balanceService->deductFunds(
                            user: $user,
                            amount: $balanceAmount,
                            source: BalanceTransactionSource::ORDER_PAYMENT,
                            description: "Balance portion of combined payment for order {$order->public_id}",
                            order: $order
                        );

                        $this->log('info', 'Balance portion deducted for combined payment', [
                            'transaction_id' => $balanceTransaction->id,
                            'amount' => $balanceAmount,
                            'user_id' => $user->id,
                        ]);
                    }

                    // Step 2: Create external payment if needed
                    if ($externalAmount > 0) {
                        // Create external payment data
                        $externalPaymentData = array_merge($data, [
                            'amount' => (int) round($externalAmount * 100), // Convert to minor units
                            'order_id' => $orderId,
                            'user_id' => $user->id,
                        ]);

                        // Get external gateway and create payment
                        $externalGateway = $this->paymentService->getGateway($externalMethod);
                        $externalPaymentResult = $externalGateway->createPayment($externalPaymentData);

                        if ($externalPaymentResult->isFailure()) {
                            // Rollback balance transaction if external payment fails
                            if ($balanceTransaction) {
                                $this->balanceService->rollbackTransaction(
                                    $balanceTransaction,
                                    'External payment failed in combined payment'
                                );
                            }

                            throw new \Exception('External payment failed: ' . $externalPaymentResult->message);
                        }

                        $this->log('info', 'External payment created for combined payment', [
                            'external_payment_id' => $externalPaymentResult->paymentId,
                            'external_method' => $externalMethod,
                            'amount' => $externalAmount,
                        ]);
                    }

                    // Generate combined payment ID
                    $combinedPaymentId = 'combined_' . uniqid();

                    // Determine overall status
                    $status = 'paid'; // Default to paid
                    if ($externalPaymentResult && $externalPaymentResult->status !== 'paid') {
                        $status = 'pending'; // If external payment is pending, combined is pending
                    }

                    $this->log('info', 'Combined payment created successfully', [
                        'combined_payment_id' => $combinedPaymentId,
                        'balance_transaction_id' => $balanceTransaction?->id,
                        'external_payment_id' => $externalPaymentResult?->paymentId,
                        'status' => $status,
                    ]);

                    return PaymentResult::success(
                        paymentId: $combinedPaymentId,
                        paymentUrl: $externalPaymentResult?->paymentUrl,
                        status: $status,
                        data: [
                            'order_id' => $orderId,
                            'balance_amount' => $balanceAmount,
                            'external_amount' => $externalAmount,
                            'balance_transaction_id' => $balanceTransaction?->id,
                            'external_payment_id' => $externalPaymentResult?->paymentId,
                            'external_payment_url' => $externalPaymentResult?->paymentUrl,
                            'external_method' => $externalMethod,
                            'requires_external_confirmation' => $status === 'pending',
                        ]
                    );

                } catch (\Exception $e) {
                    // Rollback balance transaction if something goes wrong
                    if ($balanceTransaction) {
                        $this->balanceService->rollbackTransaction(
                            $balanceTransaction,
                            'Combined payment failed: ' . $e->getMessage()
                        );
                    }
                    throw $e;
                }
            });

        } catch (\Exception $e) {
            $this->log('error', 'Combined payment creation exception', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            return PaymentResult::failure(
                message: 'Combined payment failed: ' . $e->getMessage(),
                errorCode: 'COMBINED_PAYMENT_EXCEPTION'
            );
        }
    }

    /**
     * Cancel a combined payment.
     */
    public function cancelPayment(string $paymentId): PaymentResult
    {
        try {
            $this->log('info', 'Cancelling combined payment', ['payment_id' => $paymentId]);

            // For now, return a simple cancellation
            // In a real implementation, you'd need to:
            // 1. Parse the combined payment ID to get component payment IDs
            // 2. Cancel/refund the external payment
            // 3. Refund the balance portion

            return PaymentResult::success(
                paymentId: $paymentId,
                status: 'cancelled',
                data: [
                    'message' => 'Combined payment cancellation requires manual processing',
                ]
            );

        } catch (\Exception $e) {
            $this->log('error', 'Combined payment cancellation exception', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId
            ]);

            return PaymentResult::failure(
                message: 'Combined payment cancellation failed: ' . $e->getMessage(),
                errorCode: 'COMBINED_CANCEL_EXCEPTION'
            );
        }
    }

    /**
     * Get combined payment status.
     */
    public function getPaymentStatus(string $paymentId): PaymentResult
    {
        try {
            $this->log('info', 'Getting combined payment status', ['payment_id' => $paymentId]);

            // For now, return a simple status
            // In a real implementation, you'd need to:
            // 1. Parse the combined payment ID to get component payment IDs
            // 2. Check the status of the external payment
            // 3. Return combined status

            return PaymentResult::success(
                paymentId: $paymentId,
                status: 'paid', // Simplified for now
                data: [
                    'message' => 'Combined payment status check requires component analysis',
                ]
            );

        } catch (\Exception $e) {
            $this->log('error', 'Combined payment status check exception', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId
            ]);

            return PaymentResult::failure(
                message: 'Combined payment status check failed: ' . $e->getMessage(),
                errorCode: 'COMBINED_STATUS_EXCEPTION'
            );
        }
    }

    /**
     * Combined gateway supports refunds.
     */
    public function supportsRefunds(): bool
    {
        return true;
    }

    /**
     * Handle webhook for combined payments.
     */
    public function handleWebhook(array $data): WebhookResult
    {
        $this->log('info', 'Combined gateway webhook received', $data);

        // Combined payments would need to route webhooks to the appropriate external gateway
        return WebhookResult::failure(
            'Combined gateway webhook handling requires component routing',
            'WEBHOOK_ROUTING_REQUIRED',
            ['webhook_data' => $data]
        );
    }
}
