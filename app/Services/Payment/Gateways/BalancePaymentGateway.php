<?php

namespace App\Services\Payment\Gateways;

use App\Enums\BalanceTransactionSource;
use App\Models\Order;
use App\Models\User;
use App\Services\BalanceService;
use App\Services\Payment\AbstractPaymentGateway;
use App\Services\Payment\PaymentResult;
use App\Services\Payment\WebhookResult;
use Illuminate\Support\Facades\DB;

/**
 * Balance Payment Gateway - handles payments using user's balance only.
 */
class BalancePaymentGateway extends AbstractPaymentGateway
{
    private BalanceService $balanceService;

    public function __construct()
    {
        $this->balanceService = app(BalanceService::class);
    }

    public function getName(): string
    {
        return 'balance';
    }

    /**
     * Create a balance payment.
     *
     * @param array $data Payment data containing order_id, amount, user_id
     * @return PaymentResult
     */
    public function createPayment(array $data): PaymentResult
    {
        try {
            $this->log('info', 'Creating balance payment', $data);

            $orderId = $data['order_id'] ?? throw new \InvalidArgumentException('order_id is required');
            $amount = $data['amount'] ?? throw new \InvalidArgumentException('amount is required');
            $userId = $data['user_id'] ?? throw new \InvalidArgumentException('user_id is required');

            // Get user and order
            $user = User::findOrFail($userId);
            $order = Order::findOrFail($orderId);

            // Convert amount to major units for balance service
            $amountInMajorUnits = $amount / 100;

            // Check if user can afford the payment
            if (!$this->balanceService->canAfford($user, $amountInMajorUnits)) {
                $this->log('warning', 'Insufficient balance for payment', [
                    'user_id' => $userId,
                    'required_amount' => $amountInMajorUnits,
                    'user_balance' => $this->balanceService->getCurrentUserBalance($user),
                ]);

                return PaymentResult::failure(
                    message: 'Insufficient balance',
                    errorCode: 'INSUFFICIENT_BALANCE'
                );
            }

            return DB::transaction(function () use ($user, $order, $amountInMajorUnits, $orderId, $amount) {
                // Deduct funds from user balance
                $transaction = $this->balanceService->deductFunds(
                    user: $user,
                    amount: $amountInMajorUnits,
                    source: BalanceTransactionSource::ORDER_PAYMENT,
                    description: "Payment for order {$order->public_id}",
                    order: $order
                );

                // Generate a balance payment ID
                $paymentId = 'balance_' . $transaction->id;

                $this->log('info', 'Balance payment created successfully', [
                    'payment_id' => $paymentId,
                    'transaction_id' => $transaction->id,
                    'order_id' => $orderId,
                    'amount' => $amount,
                    'user_id' => $user->id,
                ]);

                return PaymentResult::success(
                    paymentId: $paymentId,
                    status: 'paid', // Balance payments are immediately paid
                    data: [
                        'order_id' => $orderId,
                        'amount' => $amount,
                        'user_id' => $user->id,
                        'transaction_id' => $transaction->id,
                        'balance_before' => $transaction->balance_before,
                        'balance_after' => $transaction->balance_after,
                        'auto_confirmed' => true,
                    ]
                );
            });

        } catch (\Exception $e) {
            $this->log('error', 'Balance payment creation exception', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            return PaymentResult::failure(
                message: 'Balance payment failed: ' . $e->getMessage(),
                errorCode: 'BALANCE_PAYMENT_EXCEPTION'
            );
        }
    }

    /**
     * Cancel a balance payment (refund to balance).
     */
    public function cancelPayment(string $paymentId): PaymentResult
    {
        try {
            $this->log('info', 'Cancelling balance payment', ['payment_id' => $paymentId]);

            // Extract transaction ID from payment ID
            if (!str_starts_with($paymentId, 'balance_')) {
                return PaymentResult::failure('Invalid balance payment ID');
            }

            $transactionId = str_replace('balance_', '', $paymentId);
            $transaction = \App\Models\BalanceTransaction::findOrFail($transactionId);

            // Create a refund transaction
            $refundTransaction = $this->balanceService->addFunds(
                user: $transaction->user,
                amount: $transaction->amount / 100, // Convert to major units
                source: BalanceTransactionSource::REFUND,
                description: "Refund for cancelled payment {$paymentId}",
                order: $transaction->order
            );

            $this->log('info', 'Balance payment cancelled successfully', [
                'payment_id' => $paymentId,
                'original_transaction_id' => $transactionId,
                'refund_transaction_id' => $refundTransaction->id,
            ]);

            return PaymentResult::success(
                paymentId: $paymentId,
                status: 'cancelled',
                data: [
                    'refund_transaction_id' => $refundTransaction->id,
                    'refunded_amount' => $transaction->amount,
                ]
            );

        } catch (\Exception $e) {
            $this->log('error', 'Balance payment cancellation exception', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId
            ]);

            return PaymentResult::failure(
                message: 'Balance payment cancellation failed: ' . $e->getMessage(),
                errorCode: 'BALANCE_CANCEL_EXCEPTION'
            );
        }
    }

    /**
     * Get balance payment status.
     */
    public function getPaymentStatus(string $paymentId): PaymentResult
    {
        try {
            $this->log('info', 'Getting balance payment status', ['payment_id' => $paymentId]);

            // Balance payments are always immediately paid
            return PaymentResult::success(
                paymentId: $paymentId,
                status: 'paid',
                data: [
                    'auto_confirmed' => true,
                ]
            );

        } catch (\Exception $e) {
            $this->log('error', 'Balance payment status check exception', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId
            ]);

            return PaymentResult::failure(
                message: 'Balance payment status check failed: ' . $e->getMessage(),
                errorCode: 'BALANCE_STATUS_EXCEPTION'
            );
        }
    }

    /**
     * Balance gateway supports refunds.
     */
    public function supportsRefunds(): bool
    {
        return true;
    }

    /**
     * Refund a balance payment.
     */
    public function refundPayment(string $paymentId, ?int $amount = null): PaymentResult
    {
        try {
            $this->log('info', 'Refunding balance payment', [
                'payment_id' => $paymentId,
                'amount' => $amount
            ]);

            // Extract transaction ID from payment ID
            if (!str_starts_with($paymentId, 'balance_')) {
                return PaymentResult::failure('Invalid balance payment ID');
            }

            $transactionId = str_replace('balance_', '', $paymentId);
            $transaction = \App\Models\BalanceTransaction::findOrFail($transactionId);

            // Determine refund amount
            $refundAmount = $amount ? $amount / 100 : $transaction->amount / 100; // Convert to major units

            // Create a refund transaction
            $refundTransaction = $this->balanceService->addFunds(
                user: $transaction->user,
                amount: $refundAmount,
                source: BalanceTransactionSource::REFUND,
                description: "Refund for payment {$paymentId}",
                order: $transaction->order
            );

            $this->log('info', 'Balance payment refunded successfully', [
                'payment_id' => $paymentId,
                'original_transaction_id' => $transactionId,
                'refund_transaction_id' => $refundTransaction->id,
                'refund_amount' => $refundAmount,
            ]);

            return PaymentResult::success(
                paymentId: $paymentId,
                status: 'refunded',
                data: [
                    'refund_transaction_id' => $refundTransaction->id,
                    'refunded_amount' => $refundTransaction->amount,
                ]
            );

        } catch (\Exception $e) {
            $this->log('error', 'Balance payment refund exception', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
                'amount' => $amount
            ]);

            return PaymentResult::failure(
                message: 'Balance payment refund failed: ' . $e->getMessage(),
                errorCode: 'BALANCE_REFUND_EXCEPTION'
            );
        }
    }

    /**
     * Handle webhook (balance payments don't use webhooks).
     */
    public function handleWebhook(array $data): WebhookResult
    {
        $this->log('info', 'Balance gateway webhook received (ignored)', $data);

        return WebhookResult::failure(
            'Balance gateway does not process webhooks',
            'WEBHOOK_NOT_SUPPORTED',
            ['webhook_data' => $data]
        );
    }
}
