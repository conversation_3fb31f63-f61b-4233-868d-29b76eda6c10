<?php

namespace App\Exceptions\Leadteh;

use Exception;

class LeadtehApiException extends Exception
{
    protected int $apiCode;
    protected array $apiResponse;

    public function __construct(string $message, int $apiCode = 0, array $apiResponse = [], int $code = 0, ?Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->apiCode = $apiCode;
        $this->apiResponse = $apiResponse;
    }

    public function getApiCode(): int
    {
        return $this->apiCode;
    }

    public function getApiResponse(): array
    {
        return $this->apiResponse;
    }
}
