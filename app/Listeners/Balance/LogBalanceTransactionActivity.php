<?php

namespace App\Listeners\Balance;

use App\Events\Balance\BalanceTransactionCreated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class LogBalanceTransactionActivity implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * This listener logs balance transaction activity for audit purposes.
     */
    public function handle(BalanceTransactionCreated $event): void
    {
        $transaction = $event->transaction;

        try {
            Log::info('Balance transaction created', [
                'transaction_id' => $transaction->id,
                'user_id' => $transaction->user_id,
                'type' => $transaction->type->value,
                'source' => $transaction->source->value,
                'amount' => $transaction->amount,
                'balance_before' => $transaction->balance_before,
                'balance_after' => $transaction->balance_after,
                'order_id' => $transaction->order_id,
                'payment_id' => $transaction->payment_id,
                'description' => $transaction->description,
            ]);

            // Additional logging for specific transaction types
            if ($transaction->type->isCredit()) {
                Log::info('Balance credited', [
                    'user_id' => $transaction->user_id,
                    'amount' => $transaction->amount,
                    'source' => $transaction->source->label(),
                    'new_balance' => $transaction->balance_after,
                ]);
            } else {
                Log::info('Balance debited', [
                    'user_id' => $transaction->user_id,
                    'amount' => $transaction->amount,
                    'source' => $transaction->source->label(),
                    'new_balance' => $transaction->balance_after,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Failed to log balance transaction activity', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
            ]);

            // Don't re-throw - logging failures shouldn't break the transaction
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(BalanceTransactionCreated $event, \Throwable $exception): void
    {
        Log::error('Balance transaction logging job failed', [
            'transaction_id' => $event->transaction->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
