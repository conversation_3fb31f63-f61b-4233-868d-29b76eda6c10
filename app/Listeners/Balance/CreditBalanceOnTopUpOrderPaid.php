<?php

namespace App\Listeners\Balance;

use App\Enums\BalanceTransactionSource;
use App\Events\Balance\BalanceTopUpCompleted;
use App\Events\Order\OrderPaid;
use App\Services\BalanceService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class CreditBalanceOnTopUpOrderPaid implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct(
        protected BalanceService $balanceService,
    ) {
        //
    }

    /**
     * Handle the event.
     *
     * This listener processes balance top-up orders when they are paid.
     * It automatically credits the user's balance with the order amount.
     */
    public function handle(OrderPaid $event): void
    {
        $order = $event->order;

        try {
            // Check if this is a balance top-up order
            $isBalanceTopUp = $order->items()
                ->where('item_type', 'balance_topup')
                ->exists();

            if (!$isBalanceTopUp) {
                // Not a balance top-up order, skip
                return;
            }

            $user = $order->user;
            $amount = $order->total_amount;

            // Credit the user's balance
            $transaction = $this->balanceService->addFunds(
                user: $user,
                amount: $amount,
                source: BalanceTransactionSource::TOPUP,
                description: "Balance top-up from order {$order->public_id}",
                order: $order
            );

            Log::info('Balance credited from top-up order', [
                'order_id' => $order->public_id,
                'user_id' => $user->id,
                'amount' => $amount,
                'transaction_id' => $transaction->id,
                'new_balance' => $transaction->balance_after,
            ]);

            // Dispatch balance top-up completed event
            $payment = $order->payments()->where('status', 'paid')->first();
            if ($payment) {
                BalanceTopUpCompleted::dispatch($order, $payment);
            }

        } catch (\Exception $e) {
            Log::error('Failed to credit balance from top-up order', [
                'order_id' => $order->public_id,
                'user_id' => $order->user_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Re-throw the exception to mark the job as failed
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(OrderPaid $event, \Throwable $exception): void
    {
        Log::error('Balance top-up credit job failed', [
            'order_id' => $event->order->public_id,
            'user_id' => $event->order->user_id,
            'error' => $exception->getMessage(),
        ]);
    }
}
