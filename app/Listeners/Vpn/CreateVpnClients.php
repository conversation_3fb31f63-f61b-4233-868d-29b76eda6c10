<?php

namespace App\Listeners\Vpn;

use App\Events\Subscription\SubscriptionCreated;
use App\Events\Xui\VpnClientsForSubscriptionProcessed;
use App\Services\VpnClientManager;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class CreateVpnClients
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(SubscriptionCreated $event): void
    {
        Log::info('Listener CreateVpnClients called for', [
            'subscription' => $event->subscription->id,
            'user' => $event->subscription->user->id,
        ]);

        // create vpn clients for the user
        $vpnClientManager = new VpnClientManager($event->subscription->user);
        $results = $vpnClientManager->createVpnClientsOnPools($event->subscription->user);

        if (!empty($results)) {
            VpnClientsForSubscriptionProcessed::dispatch($event->subscription);
        } else {
            Log::error('Some error occurred. No VPN clients created for user ' . $event->subscription->user->id);
        }
    }
}
