<?php

use App\Models\Setting;

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'test_xui_server' => [
        'address' => env('TEST_XUI_SERVER_ADDRESS'),
        'port' => env('TEST_XUI_SERVER_PORT'),
        'web_base_path' => env('TEST_XUI_SERVER_WEB_BASE_PATH'),
        'username' => env('TEST_XUI_SERVER_USERNAME'),
        'password' => env('TEST_XUI_SERVER_PASSWORD'),
    ],

    'leadteh' => [
        'api_token' => env('LEADTEH_API_TOKEN'),
        'base_url' => env('LEADTEH_BASE_URL', 'https://app.leadteh.ru/api/v1/'),
        'rate_limit_delay' => env('LEADTEH_RATE_LIMIT_DELAY', 500), // milliseconds
        'ref_enabled' => Setting::get('leadteh_ref_enabled', true), // Включена ли реферальная система
        'ref_levels' => [
            1 => Setting::get('leadteh_ref_level_1_percent', 30), // Процент вознаграждения для реферера 1-го уровня
            2 => Setting::get('leadteh_ref_level_2_percent', 10), // Процент вознаграждения для реферера 2-го уровня
            3 => Setting::get('leadteh_ref_level_3_percent', 5), // Процент вознаграждения для реферера 3-го уровня
            // 4 => Setting::get('leadteh_ref_level_4_percent', 1),
        ],
        'ref_min_amount' => Setting::get('leadteh_ref_min_amount', 1000), // Минимальная сумма для начисления реферальных в копейках 10 руб = 1000 коп

    ],

];
